#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de démarrage rapide pour l'application SOTRAMINE
"""

import os
import sys
from datetime import datetime

def check_dependencies():
    """Vérifier les dépendances"""
    try:
        import pandas
        import sqlite3
        print("✅ Toutes les dépendances sont installées")
        return True
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        print("📦 Installez les dépendances avec: pip install pandas openpyxl")
        return False

def check_database():
    """Vérifier la base de données"""
    if os.path.exists("sotramine_phosphate.db"):
        print("✅ Base de données trouvée")
        return True
    else:
        print("⚠️  Base de données non trouvée, création en cours...")
        try:
            from database_models import db_manager
            print("✅ Base de données créée avec succès")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de la création de la base de données: {e}")
            return False

def check_excel_file():
    """Vérifier le fichier Excel"""
    excel_file = "Feuille de suivi phosphate SOTRAMINE_V0.4 final.xlsx"
    if os.path.exists(excel_file):
        print("✅ Fichier Excel trouvé")
        return True
    else:
        print("⚠️  Fichier Excel non trouvé")
        print(f"📁 Recherché: {excel_file}")
        return False

def show_menu():
    """Afficher le menu de démarrage"""
    print("\n🏭 SOTRAMINE - DÉMARRAGE RAPIDE")
    print("=" * 40)
    print("1. 🚀 Lancer l'application principale")
    print("2. 📊 Importer les données Excel")
    print("3. 📋 Lecture rapide du fichier Excel")
    print("4. 🔧 Initialiser la base de données")
    print("5. ℹ️  Informations système")
    print("0. 🚪 Quitter")
    print("=" * 40)

def launch_main_app():
    """Lancer l'application principale"""
    try:
        print("\n🚀 Lancement de l'application principale...")
        from sotramine_app import main
        main()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")

def import_excel_data():
    """Importer les données Excel"""
    try:
        print("\n📊 Import des données Excel...")
        from excel_importer import main
        main()
    except Exception as e:
        print(f"❌ Erreur lors de l'import: {e}")

def quick_excel_read():
    """Lecture rapide du fichier Excel"""
    try:
        print("\n📋 Lecture rapide du fichier Excel...")
        from quick_excel_reader import main
        main()
    except Exception as e:
        print(f"❌ Erreur lors de la lecture: {e}")

def init_database():
    """Initialiser la base de données"""
    try:
        print("\n🔧 Initialisation de la base de données...")
        from database_models import DatabaseManager
        db = DatabaseManager()
        print("✅ Base de données initialisée avec succès!")
    except Exception as e:
        print(f"❌ Erreur lors de l'initialisation: {e}")

def show_system_info():
    """Afficher les informations système"""
    print("\n💻 INFORMATIONS SYSTÈME")
    print("=" * 30)
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Répertoire: {os.getcwd()}")
    print(f"⏰ Date/Heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Vérifier les fichiers
    files_to_check = [
        "database_models.py",
        "data_managers.py", 
        "sotramine_app.py",
        "excel_importer.py",
        "sotramine_phosphate.db",
        "Feuille de suivi phosphate SOTRAMINE_V0.4 final.xlsx"
    ]
    
    print("\n📂 FICHIERS:")
    for file in files_to_check:
        status = "✅" if os.path.exists(file) else "❌"
        print(f"{status} {file}")
    
    # Vérifier les modules
    modules_to_check = ["pandas", "sqlite3", "datetime", "os"]
    print("\n📦 MODULES:")
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module}")

def main():
    """Fonction principale"""
    print("🏭 SOTRAMINE - Application de Suivi Phosphate")
    print("=" * 50)
    print("🔧 Vérification du système...")
    
    # Vérifications initiales
    deps_ok = check_dependencies()
    db_ok = check_database()
    excel_ok = check_excel_file()
    
    if not deps_ok:
        print("\n❌ Impossible de continuer sans les dépendances")
        return
    
    print(f"\n📊 État du système:")
    print(f"  • Dépendances: {'✅' if deps_ok else '❌'}")
    print(f"  • Base de données: {'✅' if db_ok else '❌'}")
    print(f"  • Fichier Excel: {'✅' if excel_ok else '⚠️'}")
    
    # Menu principal
    while True:
        show_menu()
        choice = input("\nVotre choix: ").strip()
        
        if choice == "1":
            if db_ok:
                launch_main_app()
            else:
                print("❌ Base de données requise. Utilisez l'option 4 d'abord.")
        elif choice == "2":
            if excel_ok and db_ok:
                import_excel_data()
            else:
                print("❌ Fichier Excel et base de données requis.")
        elif choice == "3":
            if excel_ok:
                quick_excel_read()
            else:
                print("❌ Fichier Excel requis.")
        elif choice == "4":
            init_database()
            db_ok = True
        elif choice == "5":
            show_system_info()
        elif choice == "0":
            print("👋 Au revoir!")
            break
        else:
            print("❌ Choix invalide!")
        
        input("\nAppuyez sur Entrée pour continuer...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Application fermée par l'utilisateur.")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        input("Appuyez sur Entrée pour fermer...")
