#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaires de données pour l'application SOTRAMINE
Classes pour gérer chaque type de données
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
from database_models import db_manager

class ReceptionPhosphateManager:
    """Gestionnaire pour les réceptions de phosphate"""
    
    @staticmethod
    def ajouter_reception(numero_voyage: int, date_reception: date, tonnage: float,
                         numero_bl_camion: str = None, serie_camion: str = None,
                         p2o5: float = None, cao: float = None, mgo: float = None,
                         sio2: float = None, observations: str = None) -> int:
        """Ajouter une nouvelle réception de phosphate"""
        query = '''
            INSERT INTO reception_phosphate 
            (numero_voyage, date_reception, numero_bl_camion, serie_camion, tonnage,
             p2o5_pourcentage, cao_pourcentage, mgo_pourcentage, sio2_pourcentage, observations)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (numero_voyage, date_reception, numero_bl_camion, serie_camion, tonnage,
                 p2o5, cao, mgo, sio2, observations)
        return db_manager.execute_insert(query, params)
    
    @staticmethod
    def obtenir_receptions(date_debut: date = None, date_fin: date = None) -> List[Dict]:
        """Obtenir les réceptions avec filtres optionnels"""
        query = "SELECT * FROM reception_phosphate"
        params = ()
        
        if date_debut and date_fin:
            query += " WHERE date_reception BETWEEN ? AND ?"
            params = (date_debut, date_fin)
        elif date_debut:
            query += " WHERE date_reception >= ?"
            params = (date_debut,)
        elif date_fin:
            query += " WHERE date_reception <= ?"
            params = (date_fin,)
        
        query += " ORDER BY date_reception DESC"
        return db_manager.execute_query(query, params)
    
    @staticmethod
    def obtenir_statistiques_receptions(mois: int = None, annee: int = None) -> Dict:
        """Obtenir les statistiques des réceptions"""
        where_clause = ""
        params = ()
        
        if mois and annee:
            where_clause = "WHERE strftime('%m', date_reception) = ? AND strftime('%Y', date_reception) = ?"
            params = (f"{mois:02d}", str(annee))
        elif annee:
            where_clause = "WHERE strftime('%Y', date_reception) = ?"
            params = (str(annee),)
        
        query = f'''
            SELECT 
                COUNT(*) as nombre_receptions,
                SUM(tonnage) as tonnage_total,
                AVG(tonnage) as tonnage_moyen,
                AVG(p2o5_pourcentage) as p2o5_moyen,
                MIN(date_reception) as premiere_reception,
                MAX(date_reception) as derniere_reception
            FROM reception_phosphate {where_clause}
        '''
        
        results = db_manager.execute_query(query, params)
        return results[0] if results else {}

class ProductionManager:
    """Gestionnaire pour la production journalière"""
    
    @staticmethod
    def ajouter_production(date_production: date, regime_travail: int = 8,
                          total_reception_phosphate: float = None,
                          total_production_concentre: float = None,
                          total_heures_marche: float = None,
                          total_consommation_electricite: float = None,
                          total_consommation_eau: float = None) -> int:
        """Ajouter une nouvelle entrée de production"""
        annee = date_production.year
        mois = date_production.month
        jour = date_production.day
        
        query = '''
            INSERT INTO production_journaliere 
            (date_production, annee, mois, jour, regime_travail,
             total_reception_phosphate_brut, total_production_concentre,
             total_heures_marche, total_consommation_electricite, total_consommation_eau)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (date_production, annee, mois, jour, regime_travail,
                 total_reception_phosphate, total_production_concentre,
                 total_heures_marche, total_consommation_electricite, total_consommation_eau)
        return db_manager.execute_insert(query, params)
    
    @staticmethod
    def obtenir_production(date_debut: date = None, date_fin: date = None) -> List[Dict]:
        """Obtenir les données de production"""
        query = "SELECT * FROM production_journaliere"
        params = ()
        
        if date_debut and date_fin:
            query += " WHERE date_production BETWEEN ? AND ?"
            params = (date_debut, date_fin)
        
        query += " ORDER BY date_production DESC"
        return db_manager.execute_query(query, params)
    
    @staticmethod
    def calculer_rendement_mensuel(mois: int, annee: int) -> Dict:
        """Calculer le rendement mensuel"""
        query = '''
            SELECT 
                SUM(total_reception_phosphate_brut) as reception_totale,
                SUM(total_production_concentre) as production_totale,
                AVG(total_heures_marche) as heures_marche_moyenne,
                COUNT(*) as jours_production
            FROM production_journaliere 
            WHERE mois = ? AND annee = ?
        '''
        results = db_manager.execute_query(query, (mois, annee))
        data = results[0] if results else {}
        
        # Calculer le rendement
        if data.get('reception_totale') and data.get('production_totale'):
            data['rendement_pourcentage'] = (data['production_totale'] / data['reception_totale']) * 100
        
        return data

class ArretManager:
    """Gestionnaire pour les arrêts (laverie et concentrateur)"""
    
    @staticmethod
    def ajouter_arret(table_name: str, date_arret: date, total_heures_marche: float = 0,
                     total_heures_arret: float = 0, cause_arret_1: str = None,
                     duree_arret_1: float = None, cause_arret_2: str = None,
                     duree_arret_2: float = None) -> int:
        """Ajouter un arrêt (laverie ou concentrateur)"""
        annee = date_arret.year
        mois = date_arret.month
        jour = date_arret.day
        
        query = f'''
            INSERT INTO {table_name}
            (date_arret, annee, mois, jour, total_heures_marche, total_heures_arret,
             cause_arret_1, duree_arret_1, cause_arret_2, duree_arret_2)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        params = (date_arret, annee, mois, jour, total_heures_marche, total_heures_arret,
                 cause_arret_1, duree_arret_1, cause_arret_2, duree_arret_2)
        return db_manager.execute_insert(query, params)
    
    @staticmethod
    def obtenir_arrets(table_name: str, date_debut: date = None, date_fin: date = None) -> List[Dict]:
        """Obtenir les arrêts"""
        query = f"SELECT * FROM {table_name}"
        params = ()
        
        if date_debut and date_fin:
            query += " WHERE date_arret BETWEEN ? AND ?"
            params = (date_debut, date_fin)
        
        query += " ORDER BY date_arret DESC"
        return db_manager.execute_query(query, params)
    
    @staticmethod
    def statistiques_arrets(table_name: str, mois: int, annee: int) -> Dict:
        """Statistiques des arrêts pour un mois donné"""
        query = f'''
            SELECT 
                COUNT(*) as nombre_arrets,
                SUM(total_heures_arret) as total_heures_arret,
                SUM(total_heures_marche) as total_heures_marche,
                AVG(duree_arret_1) as duree_moyenne_arret
            FROM {table_name}
            WHERE mois = ? AND annee = ?
        '''
        results = db_manager.execute_query(query, (mois, annee))
        data = results[0] if results else {}
        
        # Calculer le taux de disponibilité
        if data.get('total_heures_marche') and data.get('total_heures_arret'):
            total_heures = data['total_heures_marche'] + data['total_heures_arret']
            data['taux_disponibilite'] = (data['total_heures_marche'] / total_heures) * 100
        
        return data

class VenteManager:
    """Gestionnaire pour les ventes"""
    
    @staticmethod
    def ajouter_vente(date_vente: date, client: str, quantite_demandee: float,
                     numero_commande: int = None, date_cargaison: date = None,
                     qualite_p2o5: float = None, statut: str = "Planifiée") -> int:
        """Ajouter une nouvelle vente"""
        query = '''
            INSERT INTO ventes 
            (date_vente, numero_commande, client, date_cargaison, 
             quantite_demandee, qualite_p2o5_pourcentage, statut_vente)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        '''
        params = (date_vente, numero_commande, client, date_cargaison,
                 quantite_demandee, qualite_p2o5, statut)
        return db_manager.execute_insert(query, params)
    
    @staticmethod
    def obtenir_ventes(statut: str = None) -> List[Dict]:
        """Obtenir les ventes avec filtre optionnel par statut"""
        query = "SELECT * FROM ventes"
        params = ()
        
        if statut:
            query += " WHERE statut_vente = ?"
            params = (statut,)
        
        query += " ORDER BY date_vente DESC"
        return db_manager.execute_query(query, params)
    
    @staticmethod
    def mettre_a_jour_statut_vente(vente_id: int, nouveau_statut: str) -> int:
        """Mettre à jour le statut d'une vente"""
        query = "UPDATE ventes SET statut_vente = ? WHERE id = ?"
        return db_manager.execute_update(query, (nouveau_statut, vente_id))

class BilanManager:
    """Gestionnaire pour les bilans et inventaires"""
    
    @staticmethod
    def ajouter_bilan_journalier(date_bilan: date, responsable: str,
                               production_totale: float = None,
                               consommation_totale: float = None,
                               incidents: str = None) -> int:
        """Ajouter un bilan journalier"""
        rendement = None
        if production_totale and consommation_totale:
            rendement = (production_totale / consommation_totale) * 100
        
        query = '''
            INSERT INTO bilans_journaliers 
            (date_bilan, responsable, production_totale, consommation_totale,
             rendement_pourcentage, incidents_signales)
            VALUES (?, ?, ?, ?, ?, ?)
        '''
        params = (date_bilan, responsable, production_totale, consommation_totale,
                 rendement, incidents)
        return db_manager.execute_insert(query, params)
    
    @staticmethod
    def ajouter_inventaire(date_debut: date, date_fin: date, stock_initial: float,
                          stock_final: float, entrees_totales: float,
                          sorties_totales: float) -> int:
        """Ajouter un inventaire"""
        ecart = stock_final - (stock_initial + entrees_totales - sorties_totales)
        
        query = '''
            INSERT INTO inventaires 
            (date_debut, date_fin, stock_initial, stock_final,
             entrees_totales, sorties_totales, ecart_inventaire)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        '''
        params = (date_debut, date_fin, stock_initial, stock_final,
                 entrees_totales, sorties_totales, ecart)
        return db_manager.execute_insert(query, params)
