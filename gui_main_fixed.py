#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface graphique principale SOTRAMINE - Version corrigée
Design professionnel avec gestion cohérente des layouts
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
from datetime import datetime

# Imports des modules SOTRAMINE
try:
    from database_models import DatabaseManager
    from data_managers import (
        ReceptionPhosphateManager, ProductionManager, ArretManager,
        VenteManager, BilanManager
    )
    from config import get_config, get_app_info
    from themes import (
        ThemeManager, ModernStyles, initialize_theme_system, 
        get_theme_manager, get_modern_styles
    )
except ImportError as e:
    print(f"Erreur d'import: {e}")

class SotramineGUI:
    """Interface graphique principale SOTRAMINE - Version corrigée"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.status_var = tk.StringVar(value="✅ Initialisation...")
        
        # Initialiser le système de thèmes
        try:
            initialize_theme_system(self.root)
            self.theme_manager = get_theme_manager()
            self.modern_styles = get_modern_styles()
        except Exception as e:
            print(f"Erreur thèmes: {e}")
            self.theme_manager = None
            self.modern_styles = None
        
        self.setup_main_window()
        self.create_interface()
        self.load_initial_data()
    
    def setup_main_window(self):
        """Configuration de la fenêtre principale"""
        app_info = get_app_info()
        
        # Configuration de base
        self.root.title(f"🏭 {app_info['name']} v{app_info['version']} - Interface Professionnelle")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)
        
        # Appliquer le thème si disponible
        if self.theme_manager:
            theme = self.theme_manager.get_theme()
            self.root.configure(bg=theme["colors"]["background"])
        else:
            self.root.configure(bg='#f0f0f0')
        
        # Centrer la fenêtre
        self.center_window()
        
        # Configuration du grid principal
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.root.update_idletasks()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width // 2) - 700
        y = (screen_height // 2) - 450
        self.root.geometry(f"+{x}+{y}")
    
    def create_interface(self):
        """Créer l'interface principale"""
        # Frame principal utilisant uniquement grid
        self.main_frame = tk.Frame(self.root)
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(1, weight=1)
        
        # En-tête
        self.create_header()
        
        # Navigation (gauche)
        self.create_navigation()
        
        # Zone de contenu (droite)
        self.create_content_area()
        
        # Barre de statut
        self.create_status_bar()
    
    def create_header(self):
        """Créer l'en-tête"""
        if self.theme_manager:
            theme = self.theme_manager.get_theme()
            bg_color = theme["colors"]["primary"]
            fg_color = "white"
            font_title = theme["fonts"]["title"]
            font_body = theme["fonts"]["body"]
        else:
            bg_color = "#2E86AB"
            fg_color = "white"
            font_title = ("Arial", 18, "bold")
            font_body = ("Arial", 10)
        
        # Frame d'en-tête
        header_frame = tk.Frame(
            self.main_frame,
            bg=bg_color,
            height=80
        )
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 20))
        header_frame.grid_propagate(False)
        
        # Titre
        title_label = tk.Label(
            header_frame,
            text="🏭 SOTRAMINE",
            bg=bg_color,
            fg=fg_color,
            font=font_title
        )
        title_label.place(x=20, y=20)
        
        # Sous-titre
        subtitle_label = tk.Label(
            header_frame,
            text="Système de Suivi Phosphate - Interface Professionnelle",
            bg=bg_color,
            fg=fg_color,
            font=font_body
        )
        subtitle_label.place(x=250, y=25)
        
        # Version
        app_info = get_app_info()
        version_label = tk.Label(
            header_frame,
            text=f"v{app_info['version']}",
            bg=bg_color,
            fg=fg_color,
            font=("Arial", 8)
        )
        version_label.place(relx=1.0, x=-20, y=25, anchor="ne")
    
    def create_navigation(self):
        """Créer la navigation"""
        # Frame de navigation
        nav_frame = ttk.LabelFrame(
            self.main_frame,
            text="📋 Navigation",
            padding="15"
        )
        nav_frame.grid(row=1, column=0, sticky="ns", padx=(0, 15))
        
        # Boutons de navigation
        nav_buttons = [
            ("📊", "Tableau de bord", self.show_dashboard),
            ("📦", "Réceptions", self.show_receptions),
            ("🏭", "Production", self.show_production),
            ("⚠️", "Arrêts", self.show_arrets),
            ("💰", "Ventes", self.show_ventes),
            ("📈", "Statistiques", self.show_statistics),
            ("📊", "Import Excel", self.import_excel),
        ]
        
        for i, (icon, text, command) in enumerate(nav_buttons):
            btn = tk.Button(
                nav_frame,
                text=f"{icon} {text}",
                command=command,
                width=20,
                anchor="w",
                relief="flat",
                padx=10,
                pady=8,
                cursor="hand2"
            )
            btn.grid(row=i, column=0, pady=3, sticky="ew")
            
            # Style selon le thème
            if self.theme_manager:
                theme = self.theme_manager.get_theme()
                if i == 0:  # Tableau de bord en surbrillance
                    btn.configure(
                        bg=theme["colors"]["primary"],
                        fg="white",
                        font=theme["fonts"]["body"]
                    )
                else:
                    btn.configure(
                        bg=theme["colors"]["surface"],
                        fg=theme["colors"]["text_primary"],
                        font=theme["fonts"]["body"]
                    )
    
    def create_content_area(self):
        """Créer la zone de contenu"""
        # Frame de contenu
        self.content_frame = ttk.LabelFrame(
            self.main_frame,
            text="📄 Contenu Principal",
            padding="20"
        )
        self.content_frame.grid(row=1, column=1, sticky="nsew")
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)
        
        # Afficher le tableau de bord par défaut
        self.show_dashboard()
    
    def create_status_bar(self):
        """Créer la barre de statut"""
        if self.theme_manager:
            theme = self.theme_manager.get_theme()
            bg_color = theme["colors"]["surface"]
            fg_color = theme["colors"]["text_primary"]
            font_small = theme["fonts"]["small"]
        else:
            bg_color = "#e0e0e0"
            fg_color = "#333333"
            font_small = ("Arial", 9)
        
        # Frame de statut
        status_frame = tk.Frame(
            self.main_frame,
            bg=bg_color,
            height=30,
            relief="solid",
            borderwidth=1
        )
        status_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(20, 0))
        status_frame.grid_propagate(False)
        
        # Label de statut
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            bg=bg_color,
            fg=fg_color,
            font=font_small
        )
        status_label.place(x=16, y=6)
        
        # Horloge
        self.time_var = tk.StringVar()
        time_label = tk.Label(
            status_frame,
            textvariable=self.time_var,
            bg=bg_color,
            fg=fg_color,
            font=font_small
        )
        time_label.place(relx=1.0, x=-16, y=6, anchor="ne")
        
        # Démarrer l'horloge
        self.update_clock()
    
    def update_clock(self):
        """Mettre à jour l'horloge"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_var.set(current_time)
        self.root.after(1000, self.update_clock)
    
    def clear_content(self):
        """Vider la zone de contenu"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def update_status(self, message, status_type="info"):
        """Mettre à jour le statut"""
        icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "loading": "🔄"
        }
        icon = icons.get(status_type, "ℹ️")
        self.status_var.set(f"{icon} {message}")
    
    def show_dashboard(self):
        """Afficher le tableau de bord"""
        self.clear_content()
        self.update_status("Tableau de bord chargé", "success")
        
        # Titre
        title = tk.Label(
            self.content_frame,
            text="📊 Tableau de Bord Exécutif",
            font=("Arial", 16, "bold")
        )
        title.grid(row=0, column=0, pady=(0, 30))
        
        # Message de bienvenue
        welcome_frame = tk.Frame(self.content_frame)
        welcome_frame.grid(row=1, column=0, pady=20)
        
        welcome_text = """
🎉 Bienvenue dans SOTRAMINE - Interface Professionnelle

✨ Fonctionnalités disponibles :
• 📦 Gestion des réceptions de phosphate
• 🏭 Suivi de la production journalière
• ⚠️ Gestion des arrêts et maintenance
• 💰 Planning et suivi des ventes
• 📈 Statistiques et analyses avancées
• 🎨 Thèmes professionnels personnalisables

🚀 Utilisez la navigation de gauche pour accéder aux modules.
        """
        
        welcome_label = tk.Label(
            welcome_frame,
            text=welcome_text,
            justify=tk.LEFT,
            font=("Arial", 11),
            wraplength=600
        )
        welcome_label.pack()
    
    def show_receptions(self):
        """Afficher le module réceptions"""
        self.clear_content()
        self.update_status("Module Réceptions", "info")
        
        title = tk.Label(
            self.content_frame,
            text="📦 Module Réceptions",
            font=("Arial", 14, "bold")
        )
        title.grid(row=0, column=0, pady=20)
        
        info_label = tk.Label(
            self.content_frame,
            text="Module des réceptions de phosphate\n(Interface complète disponible dans gui_main.py)",
            font=("Arial", 10)
        )
        info_label.grid(row=1, column=0, pady=20)
    
    def show_production(self):
        """Afficher le module production"""
        self.clear_content()
        self.update_status("Module Production", "info")
        
        title = tk.Label(
            self.content_frame,
            text="🏭 Module Production",
            font=("Arial", 14, "bold")
        )
        title.grid(row=0, column=0, pady=20)
        
        info_label = tk.Label(
            self.content_frame,
            text="Module de suivi de la production\n(Interface complète disponible dans gui_main.py)",
            font=("Arial", 10)
        )
        info_label.grid(row=1, column=0, pady=20)
    
    def show_arrets(self):
        """Afficher le module arrêts"""
        self.clear_content()
        self.update_status("Module Arrêts", "info")
        
        title = tk.Label(
            self.content_frame,
            text="⚠️ Module Arrêts",
            font=("Arial", 14, "bold")
        )
        title.grid(row=0, column=0, pady=20)
        
        info_label = tk.Label(
            self.content_frame,
            text="Module de gestion des arrêts\n(Interface complète disponible dans gui_main.py)",
            font=("Arial", 10)
        )
        info_label.grid(row=1, column=0, pady=20)
    
    def show_ventes(self):
        """Afficher le module ventes"""
        self.clear_content()
        self.update_status("Module Ventes", "info")
        
        title = tk.Label(
            self.content_frame,
            text="💰 Module Ventes",
            font=("Arial", 14, "bold")
        )
        title.grid(row=0, column=0, pady=20)
        
        info_label = tk.Label(
            self.content_frame,
            text="Module de gestion des ventes\n(Interface complète disponible dans gui_main.py)",
            font=("Arial", 10)
        )
        info_label.grid(row=1, column=0, pady=20)
    
    def show_statistics(self):
        """Afficher le module statistiques"""
        self.clear_content()
        self.update_status("Module Statistiques", "info")
        
        title = tk.Label(
            self.content_frame,
            text="📈 Module Statistiques",
            font=("Arial", 14, "bold")
        )
        title.grid(row=0, column=0, pady=20)
        
        info_label = tk.Label(
            self.content_frame,
            text="Module de statistiques globales\n(Interface complète disponible dans gui_main.py)",
            font=("Arial", 10)
        )
        info_label.grid(row=1, column=0, pady=20)
    
    def import_excel(self):
        """Import Excel"""
        self.update_status("Import Excel", "info")
        messagebox.showinfo("Import Excel", "Fonctionnalité d'import Excel\n(Implémentation complète dans gui_main.py)")
    
    def load_initial_data(self):
        """Charger les données initiales"""
        try:
            # Vérifier la base de données
            if os.path.exists("sotramine_phosphate.db"):
                self.update_status("Base de données connectée", "success")
            else:
                self.update_status("Base de données non trouvée", "warning")
        except Exception as e:
            self.update_status(f"Erreur: {e}", "error")
    
    def run(self):
        """Lancer l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    try:
        app = SotramineGUI()
        app.run()
    except Exception as e:
        print(f"Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
