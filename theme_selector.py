#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sélecteur de thèmes pour SOTRAMINE
Interface pour changer le design en temps réel
"""

import tkinter as tk
from tkinter import ttk, messagebox
from themes import ThemeManager, ModernStyles

class ThemeSelector:
    """Sélecteur de thèmes avec aperçu en temps réel"""
    
    def __init__(self, parent_app=None):
        self.parent_app = parent_app
        self.theme_manager = ThemeManager()
        self.window = None
        self.preview_widgets = {}
    
    def show_selector(self):
        """Afficher le sélecteur de thèmes"""
        if self.window:
            self.window.lift()
            return
        
        self.window = tk.Toplevel() if self.parent_app else tk.Tk()
        self.window.title("🎨 Sélecteur de Thèmes SOTRAMINE")
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Créer l'interface
        self.create_interface()
        
        # Gérer la fermeture
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def center_window(self):
        """Centrer la fenêtre"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (450)
        y = (self.window.winfo_screenheight() // 2) - (350)
        self.window.geometry(f"900x700+{x}+{y}")
    
    def create_interface(self):
        """Créer l'interface du sélecteur"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_label = ttk.Label(
            main_frame,
            text="🎨 Sélecteur de Thèmes SOTRAMINE",
            font=('Arial', 16, 'bold')
        )
        title_label.pack(pady=(0, 20))
        
        # Frame pour la sélection et l'aperçu
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Panel de sélection (gauche)
        self.create_selection_panel(content_frame)
        
        # Panel d'aperçu (droite)
        self.create_preview_panel(content_frame)
        
        # Boutons de contrôle
        self.create_control_buttons(main_frame)
    
    def create_selection_panel(self, parent):
        """Créer le panel de sélection"""
        selection_frame = ttk.LabelFrame(parent, text="🎯 Sélection du Thème", padding="15")
        selection_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # Liste des thèmes
        themes_label = ttk.Label(selection_frame, text="Thèmes disponibles:", font=('Arial', 10, 'bold'))
        themes_label.pack(anchor=tk.W, pady=(0, 10))
        
        # Variable pour le thème sélectionné
        self.selected_theme = tk.StringVar(value=self.theme_manager.current_theme)
        
        # Créer les boutons radio pour chaque thème
        for theme_name, theme_data in self.theme_manager.themes.items():
            theme_frame = ttk.Frame(selection_frame)
            theme_frame.pack(fill=tk.X, pady=5)
            
            # Bouton radio
            radio_btn = ttk.Radiobutton(
                theme_frame,
                text=theme_data["name"],
                variable=self.selected_theme,
                value=theme_name,
                command=self.on_theme_change
            )
            radio_btn.pack(anchor=tk.W)
            
            # Description
            desc_label = ttk.Label(
                theme_frame,
                text=theme_data["description"],
                font=('Arial', 8),
                foreground='gray'
            )
            desc_label.pack(anchor=tk.W, padx=(20, 0))
            
            # Aperçu des couleurs
            self.create_color_preview(theme_frame, theme_data["colors"])
    
    def create_color_preview(self, parent, colors):
        """Créer un aperçu des couleurs du thème"""
        color_frame = ttk.Frame(parent)
        color_frame.pack(anchor=tk.W, padx=(20, 0), pady=(5, 0))
        
        # Afficher les couleurs principales
        main_colors = ['primary', 'secondary', 'accent', 'success', 'warning', 'danger']
        
        for i, color_name in enumerate(main_colors):
            if color_name in colors:
                color_square = tk.Frame(
                    color_frame,
                    bg=colors[color_name],
                    width=20,
                    height=20,
                    relief="solid",
                    borderwidth=1
                )
                color_square.pack(side=tk.LEFT, padx=1)
                color_square.pack_propagate(False)
    
    def create_preview_panel(self, parent):
        """Créer le panel d'aperçu"""
        preview_frame = ttk.LabelFrame(parent, text="👁️ Aperçu du Thème", padding="15")
        preview_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Zone d'aperçu
        self.preview_area = tk.Frame(preview_frame, relief="solid", borderwidth=1)
        self.preview_area.pack(fill=tk.BOTH, expand=True)
        
        # Créer l'aperçu initial
        self.create_theme_preview()
    
    def create_theme_preview(self):
        """Créer l'aperçu du thème"""
        # Nettoyer l'aperçu existant
        for widget in self.preview_area.winfo_children():
            widget.destroy()
        
        # Obtenir le thème sélectionné
        theme = self.theme_manager.get_theme(self.selected_theme.get())
        colors = theme["colors"]
        fonts = theme["fonts"]
        
        # Appliquer le fond
        self.preview_area.configure(bg=colors["background"])
        
        # En-tête simulé
        header_frame = tk.Frame(
            self.preview_area,
            bg=colors["primary"],
            height=60
        )
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))
        header_frame.pack_propagate(False)
        
        header_label = tk.Label(
            header_frame,
            text="🏭 SOTRAMINE - Aperçu",
            bg=colors["primary"],
            fg="white",
            font=fonts["heading"]
        )
        header_label.pack(pady=15)
        
        # Corps simulé
        body_frame = tk.Frame(self.preview_area, bg=colors["background"])
        body_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Navigation simulée
        nav_frame = tk.Frame(body_frame, bg=colors["surface"], width=150)
        nav_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        nav_frame.pack_propagate(False)
        
        nav_title = tk.Label(
            nav_frame,
            text="📋 Navigation",
            bg=colors["surface"],
            fg=colors["text_primary"],
            font=fonts["subheading"]
        )
        nav_title.pack(pady=10)
        
        # Boutons de navigation simulés
        nav_buttons = ["📊 Tableau de bord", "📦 Réceptions", "🏭 Production"]
        for btn_text in nav_buttons:
            btn = tk.Button(
                nav_frame,
                text=btn_text,
                bg=colors["primary"],
                fg="white",
                font=fonts["body"],
                relief="flat",
                borderwidth=0,
                pady=5
            )
            btn.pack(fill=tk.X, padx=5, pady=2)
        
        # Contenu simulé
        content_frame = tk.Frame(body_frame, bg=colors["background"])
        content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)
        
        content_title = tk.Label(
            content_frame,
            text="📊 Contenu Principal",
            bg=colors["background"],
            fg=colors["text_primary"],
            font=fonts["heading"]
        )
        content_title.pack(pady=(10, 20))
        
        # Cartes simulées
        cards_frame = tk.Frame(content_frame, bg=colors["background"])
        cards_frame.pack(fill=tk.X, pady=(0, 10))
        
        card_data = [
            ("📦", "Réceptions", "152", colors["primary"]),
            ("🏭", "Production", "100", colors["secondary"])
        ]
        
        for icon, title, value, color in card_data:
            card = tk.Frame(
                cards_frame,
                bg="white",
                relief="solid",
                borderwidth=1,
                padx=10,
                pady=10
            )
            card.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
            
            # En-tête coloré
            header = tk.Frame(card, bg=color, height=3)
            header.pack(fill=tk.X, pady=(0, 5))
            
            # Icône et titre
            title_frame = tk.Frame(card, bg="white")
            title_frame.pack(fill=tk.X)
            
            icon_label = tk.Label(
                title_frame,
                text=icon,
                bg="white",
                font=("Arial", 16),
                fg=color
            )
            icon_label.pack(side=tk.LEFT)
            
            title_label = tk.Label(
                title_frame,
                text=title,
                bg="white",
                fg=colors["text_primary"],
                font=fonts["body"]
            )
            title_label.pack(side=tk.LEFT, padx=(5, 0))
            
            # Valeur
            value_label = tk.Label(
                card,
                text=value,
                bg="white",
                fg=color,
                font=("Arial", 18, "bold")
            )
            value_label.pack(pady=5)
        
        # Barre de statut simulée
        status_frame = tk.Frame(
            self.preview_area,
            bg=colors["surface"],
            height=25
        )
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=(5, 10))
        status_frame.pack_propagate(False)
        
        status_label = tk.Label(
            status_frame,
            text="✅ Aperçu du thème",
            bg=colors["surface"],
            fg=colors["text_primary"],
            font=fonts["small"]
        )
        status_label.pack(side=tk.LEFT, padx=10, pady=5)
    
    def create_control_buttons(self, parent):
        """Créer les boutons de contrôle"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Bouton Appliquer
        apply_btn = ttk.Button(
            button_frame,
            text="✅ Appliquer le Thème",
            command=self.apply_theme
        )
        apply_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Bouton Réinitialiser
        reset_btn = ttk.Button(
            button_frame,
            text="🔄 Réinitialiser",
            command=self.reset_theme
        )
        reset_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Bouton Fermer
        close_btn = ttk.Button(
            button_frame,
            text="❌ Fermer",
            command=self.on_close
        )
        close_btn.pack(side=tk.RIGHT)
        
        # Bouton Test
        test_btn = ttk.Button(
            button_frame,
            text="🧪 Tester",
            command=self.test_theme
        )
        test_btn.pack(side=tk.RIGHT, padx=(0, 10))
    
    def on_theme_change(self):
        """Gestionnaire de changement de thème"""
        self.create_theme_preview()
    
    def apply_theme(self):
        """Appliquer le thème sélectionné"""
        selected = self.selected_theme.get()
        if self.theme_manager.set_theme(selected):
            messagebox.showinfo(
                "Thème appliqué",
                f"Le thème '{self.theme_manager.themes[selected]['name']}' a été appliqué.\n"
                "Redémarrez l'application pour voir tous les changements."
            )
            
            # Si on a une application parent, essayer de mettre à jour
            if self.parent_app and hasattr(self.parent_app, 'refresh_theme'):
                self.parent_app.refresh_theme()
        else:
            messagebox.showerror("Erreur", "Impossible d'appliquer le thème")
    
    def reset_theme(self):
        """Réinitialiser au thème par défaut"""
        self.selected_theme.set("corporate_blue")
        self.create_theme_preview()
    
    def test_theme(self):
        """Tester le thème dans une nouvelle fenêtre"""
        # Sauvegarder le thème actuel
        current_theme = self.theme_manager.current_theme
        
        # Appliquer temporairement le thème sélectionné
        self.theme_manager.set_theme(self.selected_theme.get())
        
        # Lancer l'application de test
        try:
            from test_design import TestDesignApp
            test_app = TestDesignApp()
            test_app.run()
        except ImportError:
            messagebox.showerror("Erreur", "Impossible de charger l'application de test")
        
        # Restaurer le thème original
        self.theme_manager.set_theme(current_theme)
    
    def on_close(self):
        """Fermer la fenêtre"""
        if self.window:
            self.window.destroy()
            self.window = None

def main():
    """Fonction principale pour tester"""
    selector = ThemeSelector()
    selector.show_selector()
    
    if not selector.parent_app:
        selector.window.mainloop()

if __name__ == "__main__":
    main()
