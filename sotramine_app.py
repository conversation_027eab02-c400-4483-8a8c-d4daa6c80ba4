#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Application principale SOTRAMINE - Suivi Phosphate
Interface en ligne de commande pour la gestion des données
"""

import os
import sys
from datetime import datetime, date
from typing import Optional
from data_managers import (
    ReceptionPhosphateManager, ProductionManager, ArretManager,
    VenteManager, BilanManager
)
from database_models import db_manager

class SotramineApp:
    """Application principale pour la gestion SOTRAMINE"""
    
    def __init__(self):
        self.running = True
        print("🏭 Application SOTRAMINE - Suivi Phosphate")
        print("=" * 50)
    
    def afficher_menu_principal(self):
        """Afficher le menu principal"""
        print("\n📋 MENU PRINCIPAL")
        print("-" * 30)
        print("1. 📦 Gestion Réception Phosphate")
        print("2. 🏭 Gestion Production")
        print("3. ⚠️  Gestion Arrêts")
        print("4. 💰 Gestion Ventes")
        print("5. 📊 Bilans et Rapports")
        print("6. 📈 Statistiques")
        print("7. 📁 Import/Export Excel")
        print("8. ⚙️  Administration")
        print("0. 🚪 Quitter")
        print("-" * 30)
    
    def menu_reception_phosphate(self):
        """Menu pour la gestion des réceptions"""
        while True:
            print("\n📦 GESTION RÉCEPTION PHOSPHATE")
            print("1. Ajouter une réception")
            print("2. Voir les réceptions")
            print("3. Statistiques réceptions")
            print("0. Retour au menu principal")
            
            choix = input("\nVotre choix: ").strip()
            
            if choix == "1":
                self.ajouter_reception()
            elif choix == "2":
                self.afficher_receptions()
            elif choix == "3":
                self.statistiques_receptions()
            elif choix == "0":
                break
            else:
                print("❌ Choix invalide!")
    
    def ajouter_reception(self):
        """Ajouter une nouvelle réception"""
        try:
            print("\n➕ AJOUTER UNE RÉCEPTION")
            
            numero_voyage = int(input("Numéro de voyage: "))
            date_str = input("Date de réception (YYYY-MM-DD): ")
            date_reception = datetime.strptime(date_str, "%Y-%m-%d").date()
            
            tonnage = float(input("Tonnage: "))
            numero_bl = input("Numéro BL camion (optionnel): ") or None
            serie_camion = input("Série camion (optionnel): ") or None
            
            # Analyses chimiques
            print("\n🧪 Analyses chimiques (optionnel):")
            p2o5 = input("P2O5 %: ")
            p2o5 = float(p2o5) if p2o5 else None
            
            cao = input("CaO %: ")
            cao = float(cao) if cao else None
            
            mgo = input("MgO %: ")
            mgo = float(mgo) if mgo else None
            
            sio2 = input("SiO2 %: ")
            sio2 = float(sio2) if sio2 else None
            
            observations = input("Observations: ") or None
            
            # Ajouter à la base de données
            reception_id = ReceptionPhosphateManager.ajouter_reception(
                numero_voyage, date_reception, tonnage, numero_bl, serie_camion,
                p2o5, cao, mgo, sio2, observations
            )
            
            print(f"✅ Réception ajoutée avec succès (ID: {reception_id})")
            
        except ValueError as e:
            print(f"❌ Erreur de saisie: {e}")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    def afficher_receptions(self):
        """Afficher les réceptions"""
        print("\n📋 LISTE DES RÉCEPTIONS")
        
        # Demander les filtres
        date_debut_str = input("Date début (YYYY-MM-DD, optionnel): ")
        date_fin_str = input("Date fin (YYYY-MM-DD, optionnel): ")
        
        date_debut = None
        date_fin = None
        
        try:
            if date_debut_str:
                date_debut = datetime.strptime(date_debut_str, "%Y-%m-%d").date()
            if date_fin_str:
                date_fin = datetime.strptime(date_fin_str, "%Y-%m-%d").date()
        except ValueError:
            print("❌ Format de date invalide!")
            return
        
        receptions = ReceptionPhosphateManager.obtenir_receptions(date_debut, date_fin)
        
        if not receptions:
            print("ℹ️  Aucune réception trouvée.")
            return
        
        print(f"\n📊 {len(receptions)} réception(s) trouvée(s):")
        print("-" * 80)
        
        for reception in receptions:
            print(f"🆔 ID: {reception['id']} | 🚛 Voyage: {reception['numero_voyage']}")
            print(f"📅 Date: {reception['date_reception']} | ⚖️  Tonnage: {reception['tonnage']} T")
            if reception['p2o5_pourcentage']:
                print(f"🧪 P2O5: {reception['p2o5_pourcentage']}%")
            if reception['observations']:
                print(f"📝 Obs: {reception['observations']}")
            print("-" * 80)
    
    def statistiques_receptions(self):
        """Afficher les statistiques des réceptions"""
        print("\n📈 STATISTIQUES RÉCEPTIONS")
        
        try:
            mois = input("Mois (1-12, optionnel): ")
            mois = int(mois) if mois else None
            
            annee = input("Année (optionnel): ")
            annee = int(annee) if annee else None
            
            stats = ReceptionPhosphateManager.obtenir_statistiques_receptions(mois, annee)
            
            if not stats or not stats.get('nombre_receptions'):
                print("ℹ️  Aucune donnée trouvée pour cette période.")
                return
            
            print("\n📊 RÉSULTATS:")
            print(f"🔢 Nombre de réceptions: {stats['nombre_receptions']}")
            print(f"⚖️  Tonnage total: {stats['tonnage_total']:.2f} T")
            print(f"📊 Tonnage moyen: {stats['tonnage_moyen']:.2f} T")
            if stats['p2o5_moyen']:
                print(f"🧪 P2O5 moyen: {stats['p2o5_moyen']:.2f}%")
            print(f"📅 Période: {stats['premiere_reception']} → {stats['derniere_reception']}")
            
        except ValueError:
            print("❌ Valeur invalide!")
    
    def menu_production(self):
        """Menu pour la gestion de la production"""
        while True:
            print("\n🏭 GESTION PRODUCTION")
            print("1. Ajouter production journalière")
            print("2. Voir la production")
            print("3. Rendement mensuel")
            print("0. Retour au menu principal")
            
            choix = input("\nVotre choix: ").strip()
            
            if choix == "1":
                self.ajouter_production()
            elif choix == "2":
                self.afficher_production()
            elif choix == "3":
                self.rendement_mensuel()
            elif choix == "0":
                break
            else:
                print("❌ Choix invalide!")
    
    def ajouter_production(self):
        """Ajouter une production journalière"""
        try:
            print("\n➕ AJOUTER PRODUCTION JOURNALIÈRE")
            
            date_str = input("Date de production (YYYY-MM-DD): ")
            date_production = datetime.strptime(date_str, "%Y-%m-%d").date()
            
            regime = input("Régime de travail (heures, défaut 8): ")
            regime_travail = int(regime) if regime else 8
            
            reception = input("Total réception phosphate brut (T): ")
            total_reception = float(reception) if reception else None
            
            production = input("Total production concentré (T): ")
            total_production = float(production) if production else None
            
            heures_marche = input("Total heures de marche: ")
            total_heures_marche = float(heures_marche) if heures_marche else None
            
            consommation_elec = input("Consommation électricité (kWh): ")
            total_elec = float(consommation_elec) if consommation_elec else None
            
            consommation_eau = input("Consommation eau (m³): ")
            total_eau = float(consommation_eau) if consommation_eau else None
            
            production_id = ProductionManager.ajouter_production(
                date_production, regime_travail, total_reception, total_production,
                total_heures_marche, total_elec, total_eau
            )
            
            print(f"✅ Production ajoutée avec succès (ID: {production_id})")
            
        except ValueError as e:
            print(f"❌ Erreur de saisie: {e}")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    def afficher_production(self):
        """Afficher les données de production"""
        print("\n📋 DONNÉES DE PRODUCTION")
        
        productions = ProductionManager.obtenir_production()
        
        if not productions:
            print("ℹ️  Aucune donnée de production trouvée.")
            return
        
        print(f"\n📊 {len(productions)} entrée(s) trouvée(s):")
        print("-" * 80)
        
        for prod in productions[:10]:  # Afficher les 10 dernières
            print(f"📅 Date: {prod['date_production']} | ⏰ Régime: {prod['regime_travail']}h")
            if prod['total_reception_phosphate_brut']:
                print(f"📦 Réception: {prod['total_reception_phosphate_brut']} T")
            if prod['total_production_concentre']:
                print(f"🏭 Production: {prod['total_production_concentre']} T")
            print("-" * 80)
    
    def rendement_mensuel(self):
        """Calculer le rendement mensuel"""
        try:
            print("\n📈 RENDEMENT MENSUEL")
            
            mois = int(input("Mois (1-12): "))
            annee = int(input("Année: "))
            
            rendement = ProductionManager.calculer_rendement_mensuel(mois, annee)
            
            if not rendement or not rendement.get('jours_production'):
                print("ℹ️  Aucune donnée trouvée pour cette période.")
                return
            
            print(f"\n📊 RENDEMENT {mois:02d}/{annee}:")
            print(f"📦 Réception totale: {rendement.get('reception_totale', 0):.2f} T")
            print(f"🏭 Production totale: {rendement.get('production_totale', 0):.2f} T")
            print(f"📅 Jours de production: {rendement['jours_production']}")
            if rendement.get('rendement_pourcentage'):
                print(f"📈 Rendement: {rendement['rendement_pourcentage']:.2f}%")
            
        except ValueError:
            print("❌ Valeur invalide!")
    
    def run(self):
        """Lancer l'application"""
        while self.running:
            self.afficher_menu_principal()
            choix = input("\nVotre choix: ").strip()
            
            if choix == "1":
                self.menu_reception_phosphate()
            elif choix == "2":
                self.menu_production()
            elif choix == "3":
                print("🚧 Module Arrêts - En développement")
            elif choix == "4":
                print("🚧 Module Ventes - En développement")
            elif choix == "5":
                print("🚧 Module Bilans - En développement")
            elif choix == "6":
                print("🚧 Module Statistiques - En développement")
            elif choix == "7":
                print("🚧 Module Import/Export - En développement")
            elif choix == "8":
                print("🚧 Module Administration - En développement")
            elif choix == "0":
                print("👋 Au revoir!")
                self.running = False
            else:
                print("❌ Choix invalide!")

def main():
    """Fonction principale"""
    try:
        app = SotramineApp()
        app.run()
    except KeyboardInterrupt:
        print("\n\n👋 Application fermée par l'utilisateur.")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")

if __name__ == "__main__":
    main()
