@echo off
title SOTRAMINE - Version Finale
color 0B

echo.
echo ================================================
echo    🏭 SOTRAMINE - Version Finale
echo    Systeme de Suivi Phosphate Professionnel
echo ================================================
echo.

REM Vérifier Python
echo 🔍 Verification de Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERREUR: Python n'est pas installe ou pas dans le PATH
    echo.
    echo 📥 Veuillez installer Python depuis: https://python.org
    echo    Assurez-vous de cocher "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ Python detecte: 
python --version
echo.

REM Vérifier les fichiers requis
echo 🔍 Verification des fichiers...
if not exist "sotramine_final.py" (
    echo ❌ ERREUR: Fichier sotramine_final.py manquant
    pause
    exit /b 1
)

if not exist "database_models.py" (
    echo ⚠️ ATTENTION: database_models.py manquant
    echo L'application va tenter de fonctionner en mode degrade
)

if not exist "themes.py" (
    echo ⚠️ ATTENTION: themes.py manquant
    echo L'application va utiliser le theme par defaut
)

echo ✅ Fichiers principaux detectes
echo.

echo 🚀 Lancement de SOTRAMINE - Version Finale...
echo    Interface professionnelle avec themes modernes
echo    Modules complets de gestion phosphate
echo.

REM Lancer l'application
python sotramine_final.py

REM Gestion des erreurs
if errorlevel 1 (
    echo.
    echo ❌ L'application s'est fermee avec une erreur
    echo.
    echo 🔧 Solutions possibles:
    echo    1. Verifiez que tous les fichiers sont presents
    echo    2. Installez les dependances: pip install pandas openpyxl
    echo    3. Consultez RESOLUTION_PROBLEMES.md
    echo.
    pause
) else (
    echo.
    echo ✅ Application fermee normalement
)

echo.
echo 👋 Merci d'avoir utilise SOTRAMINE !
timeout /t 3 >nul
