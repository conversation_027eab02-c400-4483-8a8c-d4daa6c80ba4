#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gestionnaire de configuration avancé pour SOTRAMINE
Permet de modifier et sauvegarder les configurations
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

from config import get_config, get_app_info

class ConfigManager:
    """Gestionnaire de configuration"""
    
    def __init__(self):
        self.config_file = "user_config.json"
        self.default_config = get_config()
        self.user_config = self.load_user_config()
    
    def load_user_config(self) -> Dict[str, Any]:
        """Charger la configuration utilisateur"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Erreur lors du chargement de la configuration: {e}")
        
        return {}
    
    def save_user_config(self) -> bool:
        """Sauvegarder la configuration utilisateur"""
        try:
            # Ajouter des métadonnées
            config_to_save = {
                'metadata': {
                    'version': get_app_info()['version'],
                    'last_modified': datetime.now().isoformat(),
                    'created_by': 'SOTRAMINE Config Manager'
                },
                'config': self.user_config
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")
            return False
    
    def get_value(self, section: str, key: str, default=None):
        """Obtenir une valeur de configuration"""
        # Priorité: config utilisateur > config par défaut
        if section in self.user_config and key in self.user_config[section]:
            return self.user_config[section][key]
        
        if section in self.default_config and key in self.default_config[section]:
            return self.default_config[section][key]
        
        return default
    
    def set_value(self, section: str, key: str, value: Any):
        """Définir une valeur de configuration"""
        if section not in self.user_config:
            self.user_config[section] = {}
        
        self.user_config[section][key] = value
    
    def reset_section(self, section: str):
        """Réinitialiser une section à ses valeurs par défaut"""
        if section in self.user_config:
            del self.user_config[section]
    
    def export_config(self, file_path: str) -> bool:
        """Exporter la configuration vers un fichier"""
        try:
            export_data = {
                'metadata': {
                    'exported_at': datetime.now().isoformat(),
                    'app_version': get_app_info()['version'],
                    'export_type': 'full_config'
                },
                'default_config': self.default_config,
                'user_config': self.user_config
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            print(f"Erreur lors de l'export: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """Importer une configuration depuis un fichier"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            if 'user_config' in import_data:
                self.user_config = import_data['user_config']
                return self.save_user_config()
            
            return False
        except Exception as e:
            print(f"Erreur lors de l'import: {e}")
            return False

class ConfigGUI:
    """Interface graphique pour la configuration"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.config_manager = ConfigManager()
        self.window = None
        self.widgets = {}
    
    def show_config_window(self):
        """Afficher la fenêtre de configuration"""
        if self.window:
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("⚙️ Configuration SOTRAMINE")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Créer l'interface
        self.create_config_interface()
        
        # Gérer la fermeture
        self.window.protocol("WM_DELETE_WINDOW", self.on_close)
    
    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.window.winfo_screenheight() // 2) - (600 // 2)
        self.window.geometry(f"800x600+{x}+{y}")
    
    def create_config_interface(self):
        """Créer l'interface de configuration"""
        # Frame principal
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_label = ttk.Label(main_frame, text="⚙️ Configuration SOTRAMINE", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Notebook pour les onglets
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Créer les onglets
        self.create_ui_tab()
        self.create_modules_tab()
        self.create_alerts_tab()
        self.create_database_tab()
        self.create_advanced_tab()
        
        # Boutons
        self.create_buttons(main_frame)
    
    def create_ui_tab(self):
        """Créer l'onglet Interface"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="🖥️ Interface")
        
        # Configuration de l'affichage
        display_frame = ttk.LabelFrame(frame, text="Affichage", padding="10")
        display_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Thème
        ttk.Label(display_frame, text="Thème:").grid(row=0, column=0, sticky=tk.W, pady=2)
        theme_var = tk.StringVar(value=self.config_manager.get_value('ui', 'theme', 'modern'))
        theme_combo = ttk.Combobox(display_frame, textvariable=theme_var, 
                                  values=['modern', 'classic', 'dark'], state='readonly')
        theme_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        self.widgets['ui_theme'] = theme_var
        
        # Émojis
        emoji_var = tk.BooleanVar(value=self.config_manager.get_value('ui', 'use_emojis', True))
        emoji_check = ttk.Checkbutton(display_frame, text="Utiliser les émojis", variable=emoji_var)
        emoji_check.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)
        self.widgets['ui_emojis'] = emoji_var
        
        # Nombre d'enregistrements
        ttk.Label(display_frame, text="Enregistrements par page:").grid(row=2, column=0, sticky=tk.W, pady=2)
        records_var = tk.StringVar(value=str(self.config_manager.get_value('ui', 'max_records_display', 50)))
        records_entry = ttk.Entry(display_frame, textvariable=records_var, width=10)
        records_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        self.widgets['ui_max_records'] = records_var
    
    def create_modules_tab(self):
        """Créer l'onglet Modules"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="📊 Modules")
        
        # Canvas et scrollbar pour le contenu
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Modules
        modules = ['reception', 'production', 'maintenance', 'sales', 'inventory']
        module_names = {
            'reception': '📦 Réceptions',
            'production': '🏭 Production', 
            'maintenance': '⚠️ Maintenance',
            'sales': '💰 Ventes',
            'inventory': '📋 Inventaire'
        }
        
        for i, module in enumerate(modules):
            module_frame = ttk.LabelFrame(scrollable_frame, text=module_names[module], padding="10")
            module_frame.pack(fill=tk.X, pady=(0, 10))
            
            # Activer/désactiver le module
            enabled_var = tk.BooleanVar(value=self.config_manager.get_value('modules', f'{module}.enabled', True))
            enabled_check = ttk.Checkbutton(module_frame, text="Module activé", variable=enabled_var)
            enabled_check.pack(anchor=tk.W)
            self.widgets[f'module_{module}_enabled'] = enabled_var
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_alerts_tab(self):
        """Créer l'onglet Alertes"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="🔔 Alertes")
        
        # Seuils d'alerte
        thresholds_frame = ttk.LabelFrame(frame, text="Seuils d'alerte", padding="10")
        thresholds_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Seuil tonnage bas
        ttk.Label(thresholds_frame, text="Tonnage bas (T):").grid(row=0, column=0, sticky=tk.W, pady=2)
        tonnage_var = tk.StringVar(value=str(self.config_manager.get_value('alerts', 'low_tonnage_threshold', 25000)))
        tonnage_entry = ttk.Entry(thresholds_frame, textvariable=tonnage_var, width=15)
        tonnage_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        self.widgets['alert_low_tonnage'] = tonnage_var
        
        # Seuil qualité basse
        ttk.Label(thresholds_frame, text="Qualité P2O5 basse (%):").grid(row=1, column=0, sticky=tk.W, pady=2)
        quality_var = tk.StringVar(value=str(self.config_manager.get_value('alerts', 'low_quality_threshold', 25.0)))
        quality_entry = ttk.Entry(thresholds_frame, textvariable=quality_var, width=15)
        quality_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        self.widgets['alert_low_quality'] = quality_var
        
        # Notifications
        notif_frame = ttk.LabelFrame(frame, text="Notifications", padding="10")
        notif_frame.pack(fill=tk.X, pady=(0, 10))
        
        desktop_var = tk.BooleanVar(value=self.config_manager.get_value('alerts', 'enable_desktop_notifications', True))
        desktop_check = ttk.Checkbutton(notif_frame, text="Notifications bureau", variable=desktop_var)
        desktop_check.pack(anchor=tk.W)
        self.widgets['alert_desktop'] = desktop_var
    
    def create_database_tab(self):
        """Créer l'onglet Base de données"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="💾 Base de données")
        
        # Sauvegarde
        backup_frame = ttk.LabelFrame(frame, text="Sauvegarde", padding="10")
        backup_frame.pack(fill=tk.X, pady=(0, 10))
        
        auto_backup_var = tk.BooleanVar(value=self.config_manager.get_value('database', 'auto_backup', True))
        auto_backup_check = ttk.Checkbutton(backup_frame, text="Sauvegarde automatique", variable=auto_backup_var)
        auto_backup_check.pack(anchor=tk.W)
        self.widgets['db_auto_backup'] = auto_backup_var
        
        # Fréquence de sauvegarde
        ttk.Label(backup_frame, text="Fréquence:").pack(anchor=tk.W, pady=(10, 0))
        freq_var = tk.StringVar(value=self.config_manager.get_value('database', 'backup_frequency', 'daily'))
        freq_combo = ttk.Combobox(backup_frame, textvariable=freq_var, 
                                 values=['daily', 'weekly', 'monthly'], state='readonly')
        freq_combo.pack(anchor=tk.W, pady=2)
        self.widgets['db_backup_freq'] = freq_var
    
    def create_advanced_tab(self):
        """Créer l'onglet Avancé"""
        frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(frame, text="🔧 Avancé")
        
        # Import/Export
        io_frame = ttk.LabelFrame(frame, text="Import/Export Configuration", padding="10")
        io_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(io_frame, text="📤 Exporter configuration", 
                  command=self.export_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(io_frame, text="📥 Importer configuration", 
                  command=self.import_config).pack(side=tk.LEFT)
        
        # Réinitialisation
        reset_frame = ttk.LabelFrame(frame, text="Réinitialisation", padding="10")
        reset_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(reset_frame, text="🔄 Réinitialiser tout", 
                  command=self.reset_all_config).pack(anchor=tk.W)
    
    def create_buttons(self, parent):
        """Créer les boutons de contrôle"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="💾 Sauvegarder", 
                  command=self.save_config).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(button_frame, text="❌ Annuler", 
                  command=self.on_close).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="🔄 Réinitialiser", 
                  command=self.reset_current_tab).pack(side=tk.LEFT)
    
    def save_config(self):
        """Sauvegarder la configuration"""
        try:
            # Sauvegarder les valeurs de l'interface
            self.config_manager.set_value('ui', 'theme', self.widgets['ui_theme'].get())
            self.config_manager.set_value('ui', 'use_emojis', self.widgets['ui_emojis'].get())
            self.config_manager.set_value('ui', 'max_records_display', int(self.widgets['ui_max_records'].get()))
            
            # Sauvegarder les alertes
            self.config_manager.set_value('alerts', 'low_tonnage_threshold', float(self.widgets['alert_low_tonnage'].get()))
            self.config_manager.set_value('alerts', 'low_quality_threshold', float(self.widgets['alert_low_quality'].get()))
            self.config_manager.set_value('alerts', 'enable_desktop_notifications', self.widgets['alert_desktop'].get())
            
            # Sauvegarder la base de données
            self.config_manager.set_value('database', 'auto_backup', self.widgets['db_auto_backup'].get())
            self.config_manager.set_value('database', 'backup_frequency', self.widgets['db_backup_freq'].get())
            
            # Sauvegarder les modules
            modules = ['reception', 'production', 'maintenance', 'sales', 'inventory']
            for module in modules:
                if f'module_{module}_enabled' in self.widgets:
                    self.config_manager.set_value('modules', f'{module}.enabled', 
                                                self.widgets[f'module_{module}_enabled'].get())
            
            if self.config_manager.save_user_config():
                messagebox.showinfo("Succès", "Configuration sauvegardée avec succès!")
                self.on_close()
            else:
                messagebox.showerror("Erreur", "Erreur lors de la sauvegarde de la configuration")
                
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def export_config(self):
        """Exporter la configuration"""
        file_path = filedialog.asksaveasfilename(
            title="Exporter la configuration",
            defaultextension=".json",
            filetypes=[("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")]
        )
        
        if file_path:
            if self.config_manager.export_config(file_path):
                messagebox.showinfo("Succès", f"Configuration exportée vers:\n{file_path}")
            else:
                messagebox.showerror("Erreur", "Erreur lors de l'export")
    
    def import_config(self):
        """Importer une configuration"""
        file_path = filedialog.askopenfilename(
            title="Importer une configuration",
            filetypes=[("Fichiers JSON", "*.json"), ("Tous les fichiers", "*.*")]
        )
        
        if file_path:
            if self.config_manager.import_config(file_path):
                messagebox.showinfo("Succès", "Configuration importée avec succès!\nRedémarrez l'application pour appliquer les changements.")
                self.on_close()
            else:
                messagebox.showerror("Erreur", "Erreur lors de l'import")
    
    def reset_current_tab(self):
        """Réinitialiser l'onglet actuel"""
        current_tab = self.notebook.tab(self.notebook.select(), "text")
        response = messagebox.askyesno("Confirmer", f"Réinitialiser les paramètres de l'onglet {current_tab}?")
        
        if response:
            # Logique de réinitialisation selon l'onglet
            messagebox.showinfo("Info", "Réinitialisation effectuée")
    
    def reset_all_config(self):
        """Réinitialiser toute la configuration"""
        response = messagebox.askyesno("Confirmer", 
                                     "Réinitialiser TOUTE la configuration aux valeurs par défaut?\n"
                                     "Cette action est irréversible!")
        
        if response:
            if os.path.exists(self.config_manager.config_file):
                os.remove(self.config_manager.config_file)
            messagebox.showinfo("Succès", "Configuration réinitialisée!\nRedémarrez l'application.")
            self.on_close()
    
    def on_close(self):
        """Fermer la fenêtre"""
        if self.window:
            self.window.destroy()
            self.window = None

def main():
    """Fonction principale pour tester"""
    root = tk.Tk()
    root.withdraw()  # Cacher la fenêtre principale
    
    config_gui = ConfigGUI()
    config_gui.show_config_window()
    
    root.mainloop()

if __name__ == "__main__":
    main()
