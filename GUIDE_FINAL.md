# 🏭 SOTRAMINE - Guide d'Utilisation Version Finale

## 🎉 Bienvenue dans SOTRAMINE Version Finale

**SOTRAMINE** est votre solution professionnelle complète pour le suivi et la gestion des opérations phosphate. Cette version finale offre une interface moderne, des fonctionnalités avancées et une stabilité optimale.

---

## 🚀 Démarrage Rapide

### 📁 **Lancement de l'Application**

#### **Méthode 1 : Double-clic (Recommandée)**
```
🖱️ Double-cliquez sur : SOTRAMINE_FINAL.bat
```

#### **Méthode 2 : Ligne de commande**
```bash
python sotramine_final.py
```

#### **Méthode 3 : Depuis l'explorateur**
```
🖱️ Double-cliquez sur : sotramine_final.py
```

### ✅ **Premier Démarrage**
1. **Vérification automatique** des dépendances
2. **Installation** des modules manquants (pandas, openpyxl)
3. **Création** de la base de données si nécessaire
4. **Lancement** de l'interface moderne

---

## 🖥️ Interface Utilisateur

### 🎨 **Design Professionnel**
- **En-tête moderne** avec logo et informations système
- **Navigation latérale** intuitive avec icônes
- **Zone de contenu** adaptative et claire
- **Barre de statut** avec horloge temps réel

### 🧭 **Navigation Principale**

#### **📊 Tableau de Bord**
- **Vue d'ensemble** des statistiques clés
- **Cartes colorées** pour chaque module
- **Activité récente** et informations système
- **Accès rapide** aux fonctionnalités

#### **📦 Réceptions**
- Gestion des arrivages de phosphate
- Analyses chimiques et qualité
- Statistiques de tonnage
- Historique complet

#### **🏭 Production**
- Suivi de la production journalière
- Calculs de rendement automatiques
- Consommations (électricité, eau)
- Analyses de performance

#### **⚠️ Arrêts**
- Gestion des arrêts laverie/concentrateur
- Causes et durées d'arrêt
- Calcul de disponibilité
- Maintenance préventive

#### **💰 Ventes**
- Planning des ventes clients
- Suivi des statuts (Planifiée → Livrée)
- Gestion de la qualité P2O5
- Statistiques de livraison

#### **📈 Statistiques**
- Vue d'ensemble globale
- KPI en temps réel
- Analyses de tendances
- Rapports automatiques

---

## 🔧 Fonctionnalités Avancées

### 📊 **Import/Export de Données**

#### **Import Excel**
1. **Menu Fichier** > **📊 Importer Excel**
2. **Sélectionner** votre fichier Excel
3. **Validation** automatique des données
4. **Import** avec barre de progression

#### **Export de Données**
1. **Menu Fichier** > **📤 Exporter données**
2. **Choisir** le format (Excel, CSV)
3. **Sélectionner** les modules à exporter
4. **Sauvegarde** automatique

### 🎨 **Personnalisation**

#### **Thèmes Disponibles**
- **Corporate Blue** : Professionnel et moderne
- **Modern Dark** : Élégant pour usage prolongé
- **Elegant Green** : Naturel et apaisant

#### **Changer de Thème**
1. **Menu Outils** > **🎨 Thèmes**
2. **Aperçu** en temps réel
3. **Test** dans nouvelle fenêtre
4. **Application** instantanée

### ⚙️ **Configuration**

#### **Paramètres Personnalisables**
- **Interface** : Taille, couleurs, polices
- **Modules** : Activation/désactivation
- **Alertes** : Seuils et notifications
- **Sauvegarde** : Fréquence et dossier

#### **Accès Configuration**
1. **Menu Outils** > **⚙️ Configuration**
2. **Onglets** organisés par catégorie
3. **Aperçu** des changements
4. **Sauvegarde** automatique

---

## 🔍 Outils de Maintenance

### 💾 **Sauvegarde**

#### **Sauvegarde Manuelle**
1. **Menu Outils** > **💾 Sauvegarde**
2. **Fichier** horodaté créé automatiquement
3. **Confirmation** de réussite
4. **Stockage** dans dossier sécurisé

#### **Sauvegarde Automatique**
- **Fréquence** configurable (quotidienne, hebdomadaire)
- **Rotation** des sauvegardes anciennes
- **Vérification** d'intégrité automatique

### 🔍 **Diagnostic Système**

#### **Vérification Automatique**
1. **Menu Outils** > **🔍 Diagnostic**
2. **Test** des modules et dépendances
3. **Contrôle** de la base de données
4. **Rapport** détaillé des problèmes

#### **Réparation Automatique**
- **Recréation** de la base si corrompue
- **Réinstallation** des dépendances manquantes
- **Réinitialisation** des paramètres défaillants

---

## 📊 Utilisation des Modules

### 📦 **Module Réceptions**

#### **Ajouter une Réception**
1. **Cliquer** sur "➕ Nouvelle Réception"
2. **Remplir** les informations obligatoires
3. **Analyses chimiques** (P2O5, humidité, etc.)
4. **Validation** et sauvegarde

#### **Consulter l'Historique**
- **Tableau** avec tri par colonnes
- **Filtrage** par date et critères
- **Double-clic** pour modifier
- **Export** des données sélectionnées

### 🏭 **Module Production**

#### **Saisie Production**
1. **Date** de production
2. **Tonnages** (brut, concentré)
3. **Consommations** (électricité, eau)
4. **Calculs automatiques** de rendement

#### **Analyses de Performance**
- **Graphiques** de rendement
- **Comparaisons** mensuelles
- **Alertes** si objectifs non atteints
- **Recommandations** d'amélioration

### ⚠️ **Module Arrêts**

#### **Déclarer un Arrêt**
1. **Type** d'équipement (laverie/concentrateur)
2. **Durée** et heures de marche
3. **Causes** prédéfinies ou personnalisées
4. **Calcul automatique** de disponibilité

#### **Suivi Maintenance**
- **Planning** des maintenances préventives
- **Historique** des interventions
- **Statistiques** par équipement
- **Optimisation** des arrêts

### 💰 **Module Ventes**

#### **Planifier une Vente**
1. **Client** et quantité demandée
2. **Date** de cargaison prévue
3. **Qualité** P2O5 requise
4. **Suivi** du statut automatique

#### **Gestion des Livraisons**
- **Changement** de statut par clic droit
- **Planning** visuel par statut
- **Alertes** de retard automatiques
- **Statistiques** de performance

---

## 🎯 Conseils d'Utilisation

### ✅ **Bonnes Pratiques**

#### **Saisie de Données**
- **Vérifiez** les données avant validation
- **Utilisez** les listes déroulantes quand disponibles
- **Sauvegardez** régulièrement
- **Exportez** périodiquement vos données

#### **Navigation Efficace**
- **Raccourcis clavier** pour actions fréquentes
- **Double-clic** pour modification rapide
- **Clic droit** pour menu contextuel
- **Filtres** pour recherches ciblées

#### **Maintenance Préventive**
- **Diagnostic** mensuel du système
- **Sauvegarde** hebdomadaire des données
- **Nettoyage** des fichiers temporaires
- **Mise à jour** des dépendances

### 🚨 **Résolution de Problèmes**

#### **Application ne Démarre Pas**
1. **Vérifier** Python installé et dans PATH
2. **Installer** dépendances : `pip install pandas openpyxl`
3. **Consulter** RESOLUTION_PROBLEMES.md
4. **Utiliser** le diagnostic automatique

#### **Erreurs de Base de Données**
1. **Fermer** toutes les instances de l'application
2. **Sauvegarder** le fichier .db existant
3. **Relancer** l'application (recréation automatique)
4. **Réimporter** les données si nécessaire

#### **Interface Incorrecte**
1. **Réinitialiser** la configuration
2. **Changer** de thème temporairement
3. **Vérifier** la résolution d'écran (min 1200x700)
4. **Redémarrer** l'application

---

## 📞 Support et Ressources

### 📖 **Documentation Disponible**
- **GUIDE_FINAL.md** - Ce guide complet
- **RESOLUTION_PROBLEMES.md** - Dépannage détaillé
- **DESIGN_PROFESSIONNEL.md** - Guide des thèmes
- **MODULES_DEVELOPPES.md** - Documentation technique

### 🔧 **Outils Intégrés**
- **Diagnostic automatique** dans l'application
- **Aide contextuelle** dans chaque module
- **Messages d'erreur** explicites
- **Logs** d'activité détaillés

### 🚀 **Évolutions Futures**
- **Nouveaux modules** selon besoins métier
- **Intégrations** avec autres systèmes
- **Fonctionnalités** avancées d'analyse
- **Interface web** pour accès distant

---

## 🎉 Conclusion

### ✨ **SOTRAMINE Version Finale**
Vous disposez maintenant d'une **solution professionnelle complète** pour la gestion de vos opérations phosphate :

- **Interface moderne** et intuitive
- **Fonctionnalités complètes** de suivi
- **Personnalisation** avancée
- **Stabilité** et performance optimales

### 🚀 **Prêt pour la Production**
L'application est **immédiatement utilisable** en environnement professionnel avec toutes les garanties de fiabilité et de performance.

**🎯 Bonne utilisation de SOTRAMINE !**

---

*📅 Guide mis à jour pour la version finale - {datetime.now().strftime('%d/%m/%Y')}*
