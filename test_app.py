#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tests unitaires pour l'application SOTRAMINE
"""

import os
import sys
import sqlite3
from datetime import datetime, date
from typing import List, Dict

# Ajouter le répertoire courant au path pour les imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database_models import DatabaseManager
from data_managers import (
    ReceptionPhosphateManager, ProductionManager, ArretManager,
    VenteManager, BilanManager
)

class TestSuite:
    """Suite de tests pour l'application SOTRAMINE"""

    def __init__(self):
        self.test_db_path = "test_sotramine.db"
        self.db_manager = None
        self.tests_passed = 0
        self.tests_failed = 0
        self.test_results = []

    def setup(self):
        """Initialiser l'environnement de test"""
        print("🔧 Initialisation des tests...")

        # Supprimer la base de test si elle existe
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)

        # Créer une nouvelle base de test
        self.db_manager = DatabaseManager(self.test_db_path)

        # Remplacer l'instance globale pour les tests
        import database_models
        database_models.db_manager = self.db_manager

        print("✅ Base de données de test créée")

    def teardown(self):
        """Nettoyer après les tests"""
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
        print("🧹 Nettoyage terminé")

    def assert_true(self, condition: bool, message: str):
        """Assertion personnalisée"""
        if condition:
            self.tests_passed += 1
            self.test_results.append(f"✅ {message}")
            print(f"✅ {message}")
        else:
            self.tests_failed += 1
            self.test_results.append(f"❌ {message}")
            print(f"❌ {message}")

    def test_database_creation(self):
        """Tester la création de la base de données"""
        print("\n📊 Test: Création de la base de données")

        # Vérifier que le fichier existe
        self.assert_true(
            os.path.exists(self.test_db_path),
            "Fichier de base de données créé"
        )

        # Vérifier les tables
        tables = self.db_manager.get_all_tables()
        expected_tables = [
            'reception_phosphate', 'production_journaliere', 'arrets_laverie',
            'arrets_concentrateur', 'ventes', 'bilans_journaliers', 'inventaires'
        ]

        for table in expected_tables:
            self.assert_true(
                table in tables,
                f"Table '{table}' créée"
            )

    def test_reception_phosphate(self):
        """Tester la gestion des réceptions"""
        print("\n📦 Test: Gestion des réceptions de phosphate")

        # Ajouter une réception
        reception_id = ReceptionPhosphateManager.ajouter_reception(
            numero_voyage=1,
            date_reception=date(2025, 1, 15),
            tonnage=35000.0,
            p2o5=29.5,
            cao=50.2,
            observations="Test réception"
        )

        self.assert_true(
            reception_id > 0,
            "Réception ajoutée avec succès"
        )

        # Récupérer les réceptions
        receptions = ReceptionPhosphateManager.obtenir_receptions()
        self.assert_true(
            len(receptions) == 1,
            "Réception récupérée"
        )

        self.assert_true(
            receptions[0]['tonnage'] == 35000.0,
            "Tonnage correct"
        )

        # Tester les statistiques
        stats = ReceptionPhosphateManager.obtenir_statistiques_receptions(1, 2025)
        self.assert_true(
            stats['nombre_receptions'] == 1,
            "Statistiques correctes"
        )

    def test_production(self):
        """Tester la gestion de la production"""
        print("\n🏭 Test: Gestion de la production")

        # Ajouter une production
        production_id = ProductionManager.ajouter_production(
            date_production=date(2025, 1, 15),
            regime_travail=8,
            total_reception_phosphate=35000.0,
            total_production_concentre=26250.0,
            total_heures_marche=20.0
        )

        self.assert_true(
            production_id > 0,
            "Production ajoutée avec succès"
        )

        # Calculer le rendement
        rendement = ProductionManager.calculer_rendement_mensuel(1, 2025)
        self.assert_true(
            rendement['jours_production'] == 1,
            "Calcul de rendement correct"
        )

        expected_rendement = (26250.0 / 35000.0) * 100
        self.assert_true(
            abs(rendement['rendement_pourcentage'] - expected_rendement) < 0.01,
            f"Rendement calculé: {rendement['rendement_pourcentage']:.2f}%"
        )

    def test_arrets(self):
        """Tester la gestion des arrêts"""
        print("\n⚠️ Test: Gestion des arrêts")

        # Ajouter un arrêt laverie
        arret_id = ArretManager.ajouter_arret(
            table_name='arrets_laverie',
            date_arret=date(2025, 1, 15),
            total_heures_marche=20.0,
            total_heures_arret=4.0,
            cause_arret_1="Maintenance préventive",
            duree_arret_1=2.0
        )

        self.assert_true(
            arret_id > 0,
            "Arrêt laverie ajouté"
        )

        # Récupérer les arrêts
        arrets = ArretManager.obtenir_arrets('arrets_laverie')
        self.assert_true(
            len(arrets) == 1,
            "Arrêt récupéré"
        )

        # Statistiques d'arrêts
        stats = ArretManager.statistiques_arrets('arrets_laverie', 1, 2025)
        self.assert_true(
            stats['nombre_arrets'] == 1,
            "Statistiques d'arrêts correctes"
        )

        # Vérifier le calcul de disponibilité
        expected_disponibilite = (20.0 / (20.0 + 4.0)) * 100
        self.assert_true(
            abs(stats['taux_disponibilite'] - expected_disponibilite) < 0.01,
            f"Taux de disponibilité: {stats['taux_disponibilite']:.2f}%"
        )

    def test_ventes(self):
        """Tester la gestion des ventes"""
        print("\n💰 Test: Gestion des ventes")

        # Ajouter une vente
        vente_id = VenteManager.ajouter_vente(
            date_vente=date(2025, 1, 20),
            client="Client Test",
            quantite_demandee=4000.0,
            qualite_p2o5=29.0,
            statut="Planifiée"
        )

        self.assert_true(
            vente_id > 0,
            "Vente ajoutée avec succès"
        )

        # Récupérer les ventes
        ventes = VenteManager.obtenir_ventes()
        self.assert_true(
            len(ventes) == 1,
            "Vente récupérée"
        )

        # Mettre à jour le statut
        rows_affected = VenteManager.mettre_a_jour_statut_vente(vente_id, "Confirmée")
        self.assert_true(
            rows_affected == 1,
            "Statut de vente mis à jour"
        )

    def test_bilans(self):
        """Tester la gestion des bilans"""
        print("\n📊 Test: Gestion des bilans")

        # Ajouter un bilan journalier
        bilan_id = BilanManager.ajouter_bilan_journalier(
            date_bilan=date(2025, 1, 15),
            responsable="Test Manager",
            production_totale=26250.0,
            consommation_totale=35000.0
        )

        self.assert_true(
            bilan_id > 0,
            "Bilan journalier ajouté"
        )

        # Ajouter un inventaire
        inventaire_id = BilanManager.ajouter_inventaire(
            date_debut=date(2025, 1, 1),
            date_fin=date(2025, 1, 31),
            stock_initial=100000.0,
            stock_final=95000.0,
            entrees_totales=35000.0,
            sorties_totales=40000.0
        )

        self.assert_true(
            inventaire_id > 0,
            "Inventaire ajouté"
        )

    def test_data_integrity(self):
        """Tester l'intégrité des données"""
        print("\n🔒 Test: Intégrité des données")

        # Tester les contraintes de dates
        try:
            # Date future (devrait fonctionner)
            ReceptionPhosphateManager.ajouter_reception(
                numero_voyage=999,
                date_reception=date(2025, 12, 31),
                tonnage=1000.0
            )
            self.assert_true(True, "Date future acceptée")
        except Exception as e:
            self.assert_true(False, f"Erreur date future: {e}")

        # Tester les valeurs négatives
        try:
            ProductionManager.ajouter_production(
                date_production=date(2025, 1, 16),
                total_reception_phosphate=-1000.0  # Valeur négative
            )
            # Si ça passe, c'est un problème (on devrait valider)
            print("⚠️ Attention: valeurs négatives acceptées")
        except Exception:
            self.assert_true(True, "Validation des valeurs négatives")

    def run_all_tests(self):
        """Exécuter tous les tests"""
        print("🧪 DÉBUT DES TESTS SOTRAMINE")
        print("=" * 50)

        self.setup()

        try:
            self.test_database_creation()
            self.test_reception_phosphate()
            self.test_production()
            self.test_arrets()
            self.test_ventes()
            self.test_bilans()
            self.test_data_integrity()

        except Exception as e:
            print(f"❌ Erreur lors des tests: {e}")
            self.tests_failed += 1

        finally:
            self.teardown()

        # Résumé des tests
        print("\n📋 RÉSUMÉ DES TESTS")
        print("=" * 30)
        print(f"✅ Tests réussis: {self.tests_passed}")
        print(f"❌ Tests échoués: {self.tests_failed}")
        print(f"📊 Total: {self.tests_passed + self.tests_failed}")

        if self.tests_failed == 0:
            print("\n🎉 Tous les tests sont passés avec succès!")
        else:
            print(f"\n⚠️ {self.tests_failed} test(s) ont échoué")

        return self.tests_failed == 0

def main():
    """Fonction principale des tests"""
    test_suite = TestSuite()
    success = test_suite.run_all_tests()

    if success:
        print("\n✅ Application prête pour la production!")
    else:
        print("\n❌ Des problèmes ont été détectés")

    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
