#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏭 SOTRAMINE - Lanceur Principal Corrigé
Version stable sans conflits de gestionnaires de géométrie
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import subprocess

def check_dependencies():
    """Vérifier les dépendances nécessaires"""
    missing_deps = []
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import openpyxl
    except ImportError:
        missing_deps.append("openpyxl")
    
    return missing_deps

def install_dependencies(missing_deps):
    """Installer les dépendances manquantes"""
    try:
        for dep in missing_deps:
            print(f"Installation de {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        return True
    except Exception as e:
        print(f"Erreur lors de l'installation: {e}")
        return False

def show_error_dialog(title, message):
    """Afficher une boîte de dialogue d'erreur"""
    root = tk.Tk()
    root.withdraw()
    messagebox.showerror(title, message)
    root.destroy()

def show_info_dialog(title, message):
    """Afficher une boîte de dialogue d'information"""
    root = tk.Tk()
    root.withdraw()
    messagebox.showinfo(title, message)
    root.destroy()

def main():
    """Fonction principale de lancement"""
    print("🏭 SOTRAMINE - Démarrage de l'application (Version Corrigée)")
    print("=" * 60)
    
    # Vérifier les dépendances
    print("🔧 Vérification des dépendances...")
    missing_deps = check_dependencies()
    
    if missing_deps:
        print(f"⚠️ Dépendances manquantes: {', '.join(missing_deps)}")
        
        # Demander à l'utilisateur s'il veut installer
        root = tk.Tk()
        root.withdraw()
        
        response = messagebox.askyesno(
            "Dépendances manquantes",
            f"Les modules suivants sont requis mais non installés:\n{', '.join(missing_deps)}\n\n"
            "Voulez-vous les installer automatiquement?\n"
            "(Cela nécessite une connexion Internet)"
        )
        
        root.destroy()
        
        if response:
            print("📦 Installation des dépendances...")
            if install_dependencies(missing_deps):
                print("✅ Dépendances installées avec succès!")
                show_info_dialog("Installation réussie", "Les dépendances ont été installées.\nL'application va maintenant démarrer.")
            else:
                show_error_dialog("Erreur d'installation", "Impossible d'installer les dépendances.\nVeuillez les installer manuellement:\npip install pandas openpyxl")
                return
        else:
            show_error_dialog("Dépendances requises", f"L'application ne peut pas fonctionner sans:\n{', '.join(missing_deps)}\n\nInstallez-les avec:\npip install {' '.join(missing_deps)}")
            return
    
    # Vérifier la base de données
    print("💾 Vérification de la base de données...")
    if not os.path.exists("sotramine_phosphate.db"):
        print("⚠️ Base de données non trouvée, création en cours...")
        try:
            from database_models import DatabaseManager
            db = DatabaseManager()
            print("✅ Base de données créée avec succès!")
        except Exception as e:
            show_error_dialog("Erreur de base de données", f"Impossible de créer la base de données:\n{e}")
            return
    else:
        print("✅ Base de données trouvée")
    
    # Lancer l'interface graphique corrigée
    print("🚀 Lancement de l'interface graphique...")
    try:
        from gui_main_fixed import SotramineGUI
        app = SotramineGUI()
        print("✅ Interface chargée avec succès!")
        app.run()
    except ImportError as e:
        show_error_dialog("Erreur de module", f"Impossible de charger l'interface graphique:\n{e}\n\nAssurez-vous que tous les fichiers sont présents.")
    except Exception as e:
        show_error_dialog("Erreur de lancement", f"Erreur lors du lancement de l'application:\n{e}")
        print(f"Détails de l'erreur: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Application fermée par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur fatale: {e}")
        show_error_dialog("Erreur fatale", f"Une erreur inattendue s'est produite:\n{e}")
        input("Appuyez sur Entrée pour fermer...")
