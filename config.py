#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration de l'application SOTRAMINE
"""

import os
from datetime import datetime

# Configuration de la base de données
DATABASE_CONFIG = {
    'db_path': 'sotramine_phosphate.db',
    'backup_path': 'backups/',
    'auto_backup': True,
    'backup_frequency': 'daily'  # daily, weekly, monthly
}

# Configuration des fichiers Excel
EXCEL_CONFIG = {
    'default_file': 'Feuille de suivi phosphate SOTRAMINE_V0.4 final.xlsx',
    'import_max_rows': 1000,
    'export_path': 'exports/',
    'date_format': '%Y-%m-%d'
}

# Configuration de l'usine
PLANT_CONFIG = {
    'name': 'SOTRAMINE',
    'location': 'BOUGRINE / USINE LAVERIE DE PHOSPHATE',
    'default_regime_travail': 8,  # heures
    'stock_location': 'BIR EL AFOU'
}

# Configuration des analyses chimiques
CHEMICAL_ANALYSIS = {
    'p2o5_min': 20.0,
    'p2o5_max': 35.0,
    'cao_min': 45.0,
    'cao_max': 55.0,
    'mgo_max': 2.0,
    'sio2_max': 5.0
}

# Configuration des alertes et notifications
ALERTS_CONFIG = {
    'low_tonnage_threshold': 25000,  # tonnes
    'high_downtime_threshold': 4,    # heures
    'low_quality_threshold': 25.0,   # % P2O5
    'high_quality_threshold': 35.0,  # % P2O5
    'enable_email_alerts': False,
    'enable_desktop_notifications': True,
    'alert_recipients': [],
    'notification_types': {
        'production_alert': True,
        'quality_alert': True,
        'maintenance_alert': True,
        'stock_alert': True,
        'system_alert': True
    },
    'alert_sounds': {
        'enabled': False,
        'warning_sound': 'warning.wav',
        'error_sound': 'error.wav',
        'success_sound': 'success.wav'
    }
}

# Configuration de l'interface
UI_CONFIG = {
    'language': 'fr',
    'date_format_display': '%d/%m/%Y',
    'date_format_input': '%Y-%m-%d',
    'number_format': '{:,.2f}',
    'percentage_format': '{:.2f}%',
    'max_records_display': 50,
    'use_emojis': True,
    'theme': 'modern',
    'window_size': '1200x800',
    'min_window_size': '1000x600',
    'auto_refresh_interval': 30,  # secondes
    'show_tooltips': True,
    'confirm_deletions': True,
    'auto_save': True,
    'colors': {
        'primary': '#2E86AB',
        'secondary': '#A23B72',
        'success': '#28A745',
        'warning': '#FFC107',
        'danger': '#DC3545',
        'info': '#17A2B8',
        'light': '#F8F9FA',
        'dark': '#343A40'
    },
    'fonts': {
        'default': ('Arial', 10),
        'heading': ('Arial', 12, 'bold'),
        'title': ('Arial', 16, 'bold'),
        'small': ('Arial', 8)
    }
}

# Configuration des rapports
REPORTS_CONFIG = {
    'output_path': 'reports/',
    'formats': ['csv', 'excel'],
    'auto_generate': True,
    'monthly_report': True,
    'weekly_report': False
}

# Configuration de sécurité
SECURITY_CONFIG = {
    'enable_user_auth': False,
    'session_timeout': 3600,  # secondes
    'max_login_attempts': 3,
    'log_user_actions': True
}

# Configuration des logs
LOGGING_CONFIG = {
    'log_level': 'INFO',
    'log_file': 'logs/sotramine.log',
    'max_log_size': 10485760,  # 10MB
    'backup_count': 5,
    'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}

# Constantes métier
BUSINESS_CONSTANTS = {
    'working_days_per_month': 26,
    'working_hours_per_day': 24,
    'target_availability': 85.0,  # %
    'target_yield': 75.0,         # %
    'maintenance_hours_per_month': 48
}

# Configuration des unités
UNITS = {
    'tonnage': 'T',
    'percentage': '%',
    'hours': 'h',
    'electricity': 'kWh',
    'water': 'm³',
    'temperature': '°C'
}

# Messages de l'application
MESSAGES = {
    'welcome': "🏭 Application SOTRAMINE - Suivi Phosphate",
    'goodbye': "👋 Au revoir!",
    'success': "✅ Opération réussie",
    'error': "❌ Erreur",
    'warning': "⚠️ Attention",
    'info': "ℹ️ Information"
}

# Configuration des modules
MODULES_CONFIG = {
    'reception': {
        'enabled': True,
        'auto_calculate_stats': True,
        'validate_chemistry': True,
        'required_fields': ['numero_voyage', 'date_reception', 'tonnage'],
        'max_tonnage_per_voyage': 50000,
        'min_tonnage_per_voyage': 1000,
        'auto_generate_voyage_number': True
    },
    'production': {
        'enabled': True,
        'auto_calculate_yield': True,
        'track_consumption': True,
        'target_yield_min': 70.0,
        'target_yield_max': 85.0,
        'alert_low_yield': True,
        'track_efficiency': True
    },
    'maintenance': {
        'enabled': True,
        'track_downtime': True,
        'categorize_causes': True,
        'planned_maintenance_hours': 8,
        'emergency_threshold_hours': 2,
        'maintenance_categories': [
            'Maintenance préventive',
            'Panne électrique',
            'Panne mécanique',
            'Nettoyage',
            'Changement de pièces',
            'Autre'
        ]
    },
    'sales': {
        'enabled': True,
        'track_quality': True,
        'manage_contracts': True,
        'default_quality_p2o5': 29.0,
        'min_quality_p2o5': 25.0,
        'max_quality_p2o5': 35.0,
        'contract_statuses': ['Planifiée', 'Confirmée', 'En cours', 'Livrée', 'Annulée']
    },
    'inventory': {
        'enabled': True,
        'auto_reconcile': True,
        'track_movements': True,
        'stock_alert_threshold': 10000,
        'max_stock_capacity': 100000,
        'inventory_frequency': 'monthly'
    }
}

def get_config(section: str = None):
    """
    Obtenir la configuration

    Args:
        section: Section spécifique (optionnel)

    Returns:
        dict: Configuration demandée
    """
    all_config = {
        'database': DATABASE_CONFIG,
        'excel': EXCEL_CONFIG,
        'plant': PLANT_CONFIG,
        'chemistry': CHEMICAL_ANALYSIS,
        'alerts': ALERTS_CONFIG,
        'ui': UI_CONFIG,
        'reports': REPORTS_CONFIG,
        'security': SECURITY_CONFIG,
        'logging': LOGGING_CONFIG,
        'business': BUSINESS_CONSTANTS,
        'units': UNITS,
        'messages': MESSAGES,
        'modules': MODULES_CONFIG
    }

    if section:
        return all_config.get(section, {})
    return all_config

def create_directories():
    """Créer les répertoires nécessaires"""
    directories = [
        DATABASE_CONFIG['backup_path'],
        EXCEL_CONFIG['export_path'],
        REPORTS_CONFIG['output_path'],
        os.path.dirname(LOGGING_CONFIG['log_file'])
    ]

    for directory in directories:
        if directory and not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
                print(f"📁 Répertoire créé: {directory}")
            except Exception as e:
                print(f"❌ Erreur création répertoire {directory}: {e}")

def validate_config():
    """Valider la configuration"""
    errors = []

    # Vérifier les seuils chimiques
    if CHEMICAL_ANALYSIS['p2o5_min'] >= CHEMICAL_ANALYSIS['p2o5_max']:
        errors.append("P2O5: minimum >= maximum")

    # Vérifier les seuils d'alerte
    if ALERTS_CONFIG['low_tonnage_threshold'] <= 0:
        errors.append("Seuil tonnage doit être positif")

    # Vérifier les constantes métier
    if BUSINESS_CONSTANTS['target_availability'] > 100:
        errors.append("Disponibilité cible > 100%")

    if errors:
        print("⚠️ Erreurs de configuration:")
        for error in errors:
            print(f"  • {error}")
        return False

    print("✅ Configuration validée")
    return True

def get_app_info():
    """Obtenir les informations de l'application"""
    return {
        'name': 'SOTRAMINE Phosphate Tracking',
        'version': '1.0.0',
        'author': 'Équipe SOTRAMINE',
        'description': 'Application de suivi des opérations phosphate',
        'created': '2025-01-01',
        'last_updated': datetime.now().strftime('%Y-%m-%d'),
        'python_version': '3.7+',
        'dependencies': ['pandas', 'openpyxl', 'sqlite3']
    }

if __name__ == "__main__":
    print("🔧 Configuration SOTRAMINE")
    print("=" * 30)

    # Créer les répertoires
    create_directories()

    # Valider la configuration
    validate_config()

    # Afficher les informations
    app_info = get_app_info()
    print(f"\n📱 {app_info['name']} v{app_info['version']}")
    print(f"📝 {app_info['description']}")
    print(f"👨‍💻 {app_info['author']}")
    print(f"📅 Dernière mise à jour: {app_info['last_updated']}")

    # Afficher la configuration principale
    print(f"\n⚙️ Configuration:")
    print(f"  • Base de données: {DATABASE_CONFIG['db_path']}")
    print(f"  • Fichier Excel: {EXCEL_CONFIG['default_file']}")
    print(f"  • Usine: {PLANT_CONFIG['name']} - {PLANT_CONFIG['location']}")
    print(f"  • Langue: {UI_CONFIG['language']}")
    print(f"  • Émojis: {'✅' if UI_CONFIG['use_emojis'] else '❌'}")
