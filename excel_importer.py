#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Module d'import des données Excel vers la base de données SOTRAMINE
"""

import pandas as pd
import os
from datetime import datetime, date
from typing import Dict, List, Optional
from data_managers import (
    ReceptionPhosphateManager, ProductionManager, ArretManager,
    VenteManager, BilanManager
)

class ExcelImporter:
    """Importateur de données Excel vers la base de données"""

    def __init__(self, excel_file_path: str):
        self.excel_file_path = excel_file_path
        self.excel_file = None
        self.import_stats = {
            'receptions': 0,
            'productions': 0,
            'arrets_laverie': 0,
            'arrets_concentrateur': 0,
            'ventes': 0,
            'erreurs': []
        }

    def charger_fichier_excel(self) -> bool:
        """Charger le fichier Excel"""
        try:
            if not os.path.exists(self.excel_file_path):
                print(f"❌ Fichier non trouvé: {self.excel_file_path}")
                return False

            self.excel_file = pd.ExcelFile(self.excel_file_path)
            print(f"✅ Fichier Excel chargé: {self.excel_file_path}")
            print(f"📊 Feuilles disponibles: {self.excel_file.sheet_names}")
            return True

        except Exception as e:
            print(f"❌ Erreur lors du chargement: {e}")
            return False

    def importer_receptions_phosphate(self) -> int:
        """Importer les données de réception de phosphate"""
        try:
            print("\n📦 Import des réceptions de phosphate...")

            # Lire la feuille avec un nombre limité de lignes
            df = pd.read_excel(self.excel_file_path, sheet_name='Saisie_Recep_Phos', nrows=200)
            df_clean = df.dropna(how='all')

            count = 0

            # Identifier les lignes de données (après les en-têtes)
            for index, row in df_clean.iterrows():
                try:
                    # Vérifier si c'est une ligne de données valide
                    if (pd.notna(row.iloc[0]) and
                        str(row.iloc[0]).isdigit() and
                        pd.notna(row.iloc[1])):

                        numero_voyage = int(row.iloc[0])

                        # Convertir la date
                        date_reception = None
                        if pd.notna(row.iloc[1]):
                            if isinstance(row.iloc[1], datetime):
                                date_reception = row.iloc[1].date()
                            else:
                                date_reception = pd.to_datetime(row.iloc[1]).date()

                        if not date_reception:
                            continue

                        # Extraire les autres données
                        numero_bl = str(row.iloc[2]) if pd.notna(row.iloc[2]) else None
                        serie_camion = str(row.iloc[3]) if pd.notna(row.iloc[3]) else None
                        tonnage = float(row.iloc[4]) if pd.notna(row.iloc[4]) else 0.0

                        # Analyses chimiques (colonnes suivantes)
                        p2o5 = float(row.iloc[6]) if pd.notna(row.iloc[6]) and str(row.iloc[6]).replace('.', '').isdigit() else None
                        cao = float(row.iloc[8]) if pd.notna(row.iloc[8]) and str(row.iloc[8]).replace('.', '').isdigit() else None
                        mgo = float(row.iloc[10]) if pd.notna(row.iloc[10]) and str(row.iloc[10]).replace('.', '').isdigit() else None
                        sio2 = float(row.iloc[12]) if pd.notna(row.iloc[12]) and str(row.iloc[12]).replace('.', '').isdigit() else None

                        observations = str(row.iloc[13]) if pd.notna(row.iloc[13]) else None

                        # Ajouter à la base de données
                        ReceptionPhosphateManager.ajouter_reception(
                            numero_voyage, date_reception, tonnage, numero_bl, serie_camion,
                            p2o5, cao, mgo, sio2, observations
                        )

                        count += 1

                except Exception as e:
                    self.import_stats['erreurs'].append(f"Réception ligne {index}: {e}")
                    continue

            self.import_stats['receptions'] = count
            print(f"✅ {count} réceptions importées")
            return count

        except Exception as e:
            print(f"❌ Erreur lors de l'import des réceptions: {e}")
            return 0

    def importer_production(self) -> int:
        """Importer les données de production"""
        try:
            print("\n🏭 Import des données de production...")

            df = pd.read_excel(self.excel_file_path, sheet_name='Saisie_Prod', nrows=100)
            df_clean = df.dropna(how='all')

            count = 0

            for index, row in df_clean.iterrows():
                try:
                    # Vérifier si c'est une ligne de données valide
                    if pd.notna(row['Date']) and isinstance(row['Date'], datetime):

                        date_production = row['Date'].date()
                        regime_travail = int(row['Regime_Travail']) if pd.notna(row['Regime_Travail']) else 8

                        # Extraire les données de production
                        total_reception = float(row['Total_Reception_Phopshate_Brut']) if pd.notna(row['Total_Reception_Phopshate_Brut']) else None
                        total_production = float(row['T_Prod_Conc']) if pd.notna(row['T_Prod_Conc']) else None
                        total_heures_marche = float(row['T_heure_Mr']) if pd.notna(row['T_heure_Mr']) else None
                        total_elec = float(row['T_cons_Elec']) if pd.notna(row['T_cons_Elec']) else None
                        total_eau = float(row['T_cons_eau']) if pd.notna(row['T_cons_eau']) else None

                        # Ajouter à la base de données
                        ProductionManager.ajouter_production(
                            date_production, regime_travail, total_reception,
                            total_production, total_heures_marche, total_elec, total_eau
                        )

                        count += 1

                except Exception as e:
                    self.import_stats['erreurs'].append(f"Production ligne {index}: {e}")
                    continue

            self.import_stats['productions'] = count
            print(f"✅ {count} entrées de production importées")
            return count

        except Exception as e:
            print(f"❌ Erreur lors de l'import de la production: {e}")
            return 0

    def importer_arrets(self, sheet_name: str, table_name: str) -> int:
        """Importer les données d'arrêts"""
        try:
            print(f"\n⚠️  Import des arrêts ({sheet_name})...")

            df = pd.read_excel(self.excel_file_path, sheet_name=sheet_name, nrows=100)
            df_clean = df.dropna(how='all')

            count = 0

            for index, row in df_clean.iterrows():
                try:
                    if pd.notna(row['Date']) and isinstance(row['Date'], datetime):

                        date_arret = row['Date'].date()
                        total_heures_marche = float(row['T_heure_Mr']) if pd.notna(row['T_heure_Mr']) else 0
                        total_heures_arret = float(row['T_heure_AR']) if pd.notna(row['T_heure_AR']) else 0

                        cause_arret_1 = str(row['Cause_Arrêt_1']) if pd.notna(row['Cause_Arrêt_1']) else None
                        duree_arret_1 = float(row['Durée_1']) if pd.notna(row['Durée_1']) else None

                        cause_arret_2 = str(row['Cause_Arrêt_2']) if pd.notna(row['Cause_Arrêt_2']) else None
                        duree_arret_2 = float(row['Durée_2']) if pd.notna(row['Durée_2']) else None

                        # Ajouter à la base de données
                        ArretManager.ajouter_arret(
                            table_name, date_arret, total_heures_marche, total_heures_arret,
                            cause_arret_1, duree_arret_1, cause_arret_2, duree_arret_2
                        )

                        count += 1

                except Exception as e:
                    self.import_stats['erreurs'].append(f"Arrêt {sheet_name} ligne {index}: {e}")
                    continue

            print(f"✅ {count} arrêts importés pour {sheet_name}")
            return count

        except Exception as e:
            print(f"❌ Erreur lors de l'import des arrêts {sheet_name}: {e}")
            return 0

    def importer_ventes(self) -> int:
        """Importer les données de ventes"""
        try:
            print("\n💰 Import des ventes...")

            df = pd.read_excel(self.excel_file_path, sheet_name='Saisie_Vente', nrows=50)
            df_clean = df.dropna(how='all')

            count = 0

            for index, row in df_clean.iterrows():
                try:
                    # Chercher les lignes avec des données de vente valides
                    if (pd.notna(row.iloc[0]) and
                        isinstance(row.iloc[0], datetime) and
                        pd.notna(row.iloc[2])):  # Client

                        date_vente = row.iloc[0].date()
                        numero_commande = int(row.iloc[1]) if pd.notna(row.iloc[1]) and str(row.iloc[1]).isdigit() else None
                        client = str(row.iloc[2])
                        date_cargaison = None

                        if pd.notna(row.iloc[3]):
                            if isinstance(row.iloc[3], datetime):
                                date_cargaison = row.iloc[3].date()
                            else:
                                try:
                                    date_cargaison = pd.to_datetime(row.iloc[3]).date()
                                except:
                                    pass

                        quantite_demandee = float(row.iloc[4]) if pd.notna(row.iloc[4]) else 0.0
                        qualite_p2o5 = float(row.iloc[5]) if pd.notna(row.iloc[5]) and str(row.iloc[5]).replace('.', '').isdigit() else None

                        # Ajouter à la base de données
                        VenteManager.ajouter_vente(
                            date_vente, client, quantite_demandee, numero_commande,
                            date_cargaison, qualite_p2o5
                        )

                        count += 1

                except Exception as e:
                    self.import_stats['erreurs'].append(f"Vente ligne {index}: {e}")
                    continue

            self.import_stats['ventes'] = count
            print(f"✅ {count} ventes importées")
            return count

        except Exception as e:
            print(f"❌ Erreur lors de l'import des ventes: {e}")
            return 0

    def importer_tout(self) -> Dict:
        """Importer toutes les données du fichier Excel"""
        if not self.charger_fichier_excel():
            return self.import_stats

        print("\n🚀 DÉBUT DE L'IMPORT COMPLET")
        print("=" * 50)

        # Import des différentes feuilles
        self.importer_receptions_phosphate()
        self.importer_production()

        # Import des arrêts
        self.import_stats['arrets_laverie'] = self.importer_arrets('Saisie_Arrêt_Laverie', 'arrets_laverie')
        self.import_stats['arrets_concentrateur'] = self.importer_arrets('Saisie_Arrêt_Conc', 'arrets_concentrateur')

        self.importer_ventes()

        # Afficher le résumé
        print("\n📊 RÉSUMÉ DE L'IMPORT")
        print("=" * 50)
        print(f"📦 Réceptions: {self.import_stats['receptions']}")
        print(f"🏭 Productions: {self.import_stats['productions']}")
        print(f"⚠️  Arrêts laverie: {self.import_stats['arrets_laverie']}")
        print(f"⚠️  Arrêts concentrateur: {self.import_stats['arrets_concentrateur']}")
        print(f"💰 Ventes: {self.import_stats['ventes']}")

        if self.import_stats['erreurs']:
            print(f"\n❌ Erreurs ({len(self.import_stats['erreurs'])}):")
            for erreur in self.import_stats['erreurs'][:5]:  # Afficher les 5 premières
                print(f"  • {erreur}")
            if len(self.import_stats['erreurs']) > 5:
                print(f"  ... et {len(self.import_stats['erreurs']) - 5} autres erreurs")

        print("\n✅ Import terminé!")
        return self.import_stats

def main():
    """Fonction principale pour tester l'import"""
    excel_file = "Feuille de suivi phosphate SOTRAMINE_V0.4 final.xlsx"

    if not os.path.exists(excel_file):
        print(f"❌ Fichier Excel non trouvé: {excel_file}")
        return

    importer = ExcelImporter(excel_file)
    stats = importer.importer_tout()

    total_records = (stats['receptions'] + stats['productions'] +
                    stats['arrets_laverie'] + stats['arrets_concentrateur'] + stats['ventes'])
    print(f"\n📈 Import terminé avec {total_records} enregistrements")

if __name__ == "__main__":
    main()
