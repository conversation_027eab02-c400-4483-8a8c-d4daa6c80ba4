@echo off
title SOTRAMINE - Application de Suivi Phosphate
color 0A

echo.
echo ========================================
echo    SOTRAMINE - Suivi Phosphate
echo ========================================
echo.

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installé ou pas dans le PATH
    echo.
    echo Veuillez installer Python depuis: https://python.org
    echo.
    pause
    exit /b 1
)

echo Python detecte: 
python --version

echo.
echo Lancement de l'application SOTRAMINE...
echo.

REM Lancer l'application Python
python SOTRAMINE.py

REM Si l'application se ferme avec une erreur
if errorlevel 1 (
    echo.
    echo Une erreur s'est produite lors du lancement.
    echo Verifiez que tous les fichiers sont presents.
    echo.
    pause
)

echo.
echo Application fermee.
pause
