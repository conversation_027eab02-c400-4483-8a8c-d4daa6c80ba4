# 🏭 SOTRAMINE - Guide d'Utilisation Rapide

## 🚀 Démarrage de l'Application

### Méthode 1: Double-clic (Recommandée)
1. **Double-cliquez** sur `Lancer_SOTRAMINE.bat` (Windows)
2. Ou **double-cliquez** sur `SOTRAMINE.py`

### Méthode 2: Ligne de commande
```bash
python SOTRAMINE.py
```

## 📋 Interface Principale

L'application s'ouvre avec une **interface graphique moderne** comprenant :

### 🎛️ Barre de Menu
- **📁 Fichier** : Import/Export, Quitter
- **📊 Données** : Accès aux modules de données
- **📈 Rapports** : Statistiques et analyses
- **❓ Aide** : Documentation et support

### 🧭 Panel de Navigation (Gauche)
- **📊 Tableau de bord** : Vue d'ensemble
- **📦 Réceptions** : Gestion des réceptions de phosphate
- **🏭 Production** : Suivi de la production
- **⚠️ Arrêts** : Gestion des arrêts et maintenance
- **💰 Ventes** : Planning des ventes
- **📈 Statistiques** : Analyses détail<PERSON>es
- **📊 Import Excel** : Import de données

### 📄 Zone de Contenu (Droite)
Affiche le contenu du module sélectionné

## 📦 Module Réceptions

### ➕ Ajouter une Réception
1. Cliquez sur **"📦 Réceptions"** dans le menu de navigation
2. Cliquez sur **"➕ Nouvelle Réception"**
3. Remplissez le formulaire :
   - **Numéro de voyage*** (obligatoire)
   - **Date de réception*** (format YYYY-MM-DD)
   - **Tonnage*** (en tonnes)
   - **Analyses chimiques** (P2O5, CaO, MgO, SiO2 en %)
   - **Observations** (optionnel)
4. Cliquez sur **"💾 Sauvegarder"**

### ✏️ Modifier une Réception
1. Dans la liste des réceptions, **double-cliquez** sur la ligne à modifier
2. Modifiez les champs souhaités
3. Cliquez sur **"💾 Sauvegarder"**

### 📊 Voir les Statistiques
1. Cliquez sur **"📊 Statistiques"** dans le module Réceptions
2. Consultez :
   - Nombre total de réceptions
   - Tonnage total et moyen
   - Analyses chimiques moyennes
   - Répartition mensuelle

## 🏭 Module Production

### ➕ Ajouter une Production
1. Cliquez sur **"🏭 Production"** dans le menu de navigation
2. Cliquez sur **"➕ Nouvelle Production"**
3. Remplissez le formulaire :
   - **Date de production*** (obligatoire)
   - **Régime de travail** (heures)
   - **Données de production** (réception, production, heures)
   - **Consommations** (électricité, eau)
4. Cliquez sur **"💾 Sauvegarder"**

### 📈 Calculs Automatiques
L'application calcule automatiquement :
- **Rendement** = (Production / Réception) × 100
- **Taux de disponibilité**
- **Moyennes mensuelles**

## 📊 Import de Données Excel

### 📁 Importer un Fichier Excel
1. Cliquez sur **"📊 Import Excel"** ou menu **"📁 Fichier > 📊 Importer Excel"**
2. Sélectionnez votre fichier Excel (.xlsx)
3. Confirmez l'import
4. Attendez la fin du traitement
5. Consultez le rapport d'import

### 📋 Formats Supportés
L'application peut importer :
- **Réceptions de phosphate**
- **Données de production**
- **Arrêts laverie et concentrateur**
- **Ventes et planning**

## 📈 Tableau de Bord

Le tableau de bord affiche :
- **📊 Statistiques générales** (cartes de résumé)
- **📈 Activité récente** (dernières opérations)
- **💾 État de la base de données**
- **📅 Dernières réceptions**

## 🔧 Fonctionnalités Avancées

### 🔍 Recherche et Filtrage
- Utilisez les **filtres par date** dans chaque module
- **Triez** les colonnes en cliquant sur les en-têtes
- **Recherchez** des données spécifiques

### 📤 Export de Données
- Menu **"📁 Fichier > 📤 Exporter données"**
- Formats disponibles : CSV, Excel
- Export par module ou global

### 📊 Rapports
- **📋 Rapport mensuel** : Synthèse complète
- **📈 Statistiques** : Analyses détaillées
- **📄 Bilans** : Calculs automatiques

## ⚠️ Résolution de Problèmes

### 🐛 Erreurs Communes

#### "Module non trouvé"
- Vérifiez que tous les fichiers sont présents
- Réinstallez les dépendances : `pip install pandas openpyxl`

#### "Base de données inaccessible"
- Vérifiez les permissions du dossier
- Relancez l'application en tant qu'administrateur

#### "Erreur d'import Excel"
- Vérifiez le format du fichier (.xlsx)
- Assurez-vous que le fichier n'est pas ouvert dans Excel
- Vérifiez la structure des données

### 🔄 Réinitialisation
Pour réinitialiser l'application :
1. Fermez l'application
2. Supprimez le fichier `sotramine_phosphate.db`
3. Relancez l'application

## 📞 Support

### 📖 Documentation
- Consultez le fichier `README.md` pour plus de détails
- Menu **"❓ Aide > 📖 Documentation"**

### ℹ️ Informations Système
- Menu **"❓ Aide > ℹ️ À propos"**
- Affiche la version et les informations techniques

### 🔧 Configuration
- Modifiez le fichier `config.py` pour personnaliser l'application
- Changez les seuils, unités, et paramètres métier

## 💡 Conseils d'Utilisation

### ✅ Bonnes Pratiques
1. **Sauvegardez** régulièrement la base de données
2. **Importez** les données Excel par petits lots
3. **Vérifiez** les données avant validation
4. **Utilisez** les statistiques pour le suivi

### 🚀 Optimisation
- Fermez les autres applications pour de meilleures performances
- Limitez l'affichage à 50 enregistrements par vue
- Utilisez les filtres pour réduire les données affichées

### 💾 Sauvegarde
- La base de données est dans `sotramine_phosphate.db`
- Copiez ce fichier pour sauvegarder vos données
- Créez des sauvegardes avant les imports importants

---

**🎉 Félicitations ! Vous êtes maintenant prêt à utiliser SOTRAMINE efficacement !**

Pour toute question, consultez la documentation complète ou contactez l'équipe de support.
