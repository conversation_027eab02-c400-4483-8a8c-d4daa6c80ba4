#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fenêtres de dialogue pour l'interface graphique SOTRAMINE
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date
from typing import Optional, Dict, Any

from data_managers import (
    ReceptionPhosphateManager, ProductionManager, ArretManager,
    VenteManager, BilanManager
)

class BaseDialog:
    """Classe de base pour les fenêtres de dialogue"""
    
    def __init__(self, parent, title="Dialogue"):
        self.parent = parent
        self.result = None
        
        # Créer la fenêtre
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        
        # Centrer la fenêtre
        self.center_window()
        
        # Rendre la fenêtre modale
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Variables pour les champs
        self.vars = {}
        
        # Créer l'interface
        self.create_widgets()
        
        # Focus sur la fenêtre
        self.dialog.focus_set()
    
    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
    
    def create_widgets(self):
        """Créer les widgets (à surcharger)"""
        pass
    
    def validate_data(self):
        """Valider les données (à surcharger)"""
        return True
    
    def save_data(self):
        """Sauvegarder les données (à surcharger)"""
        pass
    
    def on_ok(self):
        """Gestionnaire du bouton OK"""
        if self.validate_data():
            try:
                self.result = self.save_data()
                self.dialog.destroy()
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")
    
    def on_cancel(self):
        """Gestionnaire du bouton Annuler"""
        self.dialog.destroy()

class ReceptionDialog(BaseDialog):
    """Dialogue pour ajouter/modifier une réception"""
    
    def __init__(self, parent, reception_data=None):
        self.reception_data = reception_data
        self.is_edit = reception_data is not None
        
        title = "✏️ Modifier Réception" if self.is_edit else "➕ Nouvelle Réception"
        super().__init__(parent, title)
    
    def create_widgets(self):
        """Créer l'interface de la réception"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_text = "Modifier la réception" if self.is_edit else "Ajouter une nouvelle réception"
        title_label = ttk.Label(main_frame, text=title_text, font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Variables
        self.vars['numero_voyage'] = tk.StringVar()
        self.vars['date_reception'] = tk.StringVar()
        self.vars['numero_bl'] = tk.StringVar()
        self.vars['serie_camion'] = tk.StringVar()
        self.vars['tonnage'] = tk.StringVar()
        self.vars['p2o5'] = tk.StringVar()
        self.vars['cao'] = tk.StringVar()
        self.vars['mgo'] = tk.StringVar()
        self.vars['sio2'] = tk.StringVar()
        self.vars['observations'] = tk.StringVar()
        
        # Champs obligatoires
        row = 1
        
        # Numéro de voyage
        ttk.Label(main_frame, text="Numéro de voyage *:").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['numero_voyage'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Date de réception
        ttk.Label(main_frame, text="Date de réception *:").grid(row=row, column=0, sticky=tk.W, pady=2)
        date_frame = ttk.Frame(main_frame)
        date_frame.grid(row=row, column=1, sticky=tk.W, pady=2)
        ttk.Entry(date_frame, textvariable=self.vars['date_reception'], width=20).pack(side=tk.LEFT)
        ttk.Label(date_frame, text="(YYYY-MM-DD)").pack(side=tk.LEFT, padx=(5, 0))
        row += 1
        
        # Tonnage
        ttk.Label(main_frame, text="Tonnage *:").grid(row=row, column=0, sticky=tk.W, pady=2)
        tonnage_frame = ttk.Frame(main_frame)
        tonnage_frame.grid(row=row, column=1, sticky=tk.W, pady=2)
        ttk.Entry(tonnage_frame, textvariable=self.vars['tonnage'], width=20).pack(side=tk.LEFT)
        ttk.Label(tonnage_frame, text="tonnes").pack(side=tk.LEFT, padx=(5, 0))
        row += 1
        
        # Séparateur
        ttk.Separator(main_frame, orient=tk.HORIZONTAL).grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        row += 1
        
        # Champs optionnels
        ttk.Label(main_frame, text="Informations optionnelles:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))
        row += 1
        
        # Numéro BL
        ttk.Label(main_frame, text="Numéro BL camion:").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['numero_bl'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Série camion
        ttk.Label(main_frame, text="Série camion:").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['serie_camion'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Analyses chimiques
        ttk.Label(main_frame, text="Analyses chimiques:", font=('Arial', 10, 'bold')).grid(row=row, column=0, columnspan=2, sticky=tk.W, pady=(10, 5))
        row += 1
        
        # P2O5
        ttk.Label(main_frame, text="P2O5 (%):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['p2o5'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # CaO
        ttk.Label(main_frame, text="CaO (%):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['cao'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # MgO
        ttk.Label(main_frame, text="MgO (%):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['mgo'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # SiO2
        ttk.Label(main_frame, text="SiO2 (%):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['sio2'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Observations
        ttk.Label(main_frame, text="Observations:").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['observations'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Boutons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row+1, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="💾 Sauvegarder", command=self.on_ok).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Annuler", command=self.on_cancel).pack(side=tk.LEFT)
        
        # Remplir les données si modification
        if self.is_edit and self.reception_data:
            self.populate_fields()
        else:
            # Valeurs par défaut
            self.vars['date_reception'].set(datetime.now().strftime('%Y-%m-%d'))
    
    def populate_fields(self):
        """Remplir les champs avec les données existantes"""
        data = self.reception_data
        self.vars['numero_voyage'].set(str(data.get('numero_voyage', '')))
        self.vars['date_reception'].set(str(data.get('date_reception', '')))
        self.vars['numero_bl'].set(str(data.get('numero_bl_camion', '') or ''))
        self.vars['serie_camion'].set(str(data.get('serie_camion', '') or ''))
        self.vars['tonnage'].set(str(data.get('tonnage', '') or ''))
        self.vars['p2o5'].set(str(data.get('p2o5_pourcentage', '') or ''))
        self.vars['cao'].set(str(data.get('cao_pourcentage', '') or ''))
        self.vars['mgo'].set(str(data.get('mgo_pourcentage', '') or ''))
        self.vars['sio2'].set(str(data.get('sio2_pourcentage', '') or ''))
        self.vars['observations'].set(str(data.get('observations', '') or ''))
    
    def validate_data(self):
        """Valider les données de la réception"""
        errors = []
        
        # Numéro de voyage
        try:
            numero_voyage = int(self.vars['numero_voyage'].get())
            if numero_voyage <= 0:
                errors.append("Le numéro de voyage doit être positif")
        except ValueError:
            errors.append("Le numéro de voyage doit être un nombre entier")
        
        # Date
        try:
            date_str = self.vars['date_reception'].get()
            datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            errors.append("La date doit être au format YYYY-MM-DD")
        
        # Tonnage
        try:
            tonnage = float(self.vars['tonnage'].get())
            if tonnage <= 0:
                errors.append("Le tonnage doit être positif")
        except ValueError:
            errors.append("Le tonnage doit être un nombre")
        
        # Analyses chimiques (optionnelles)
        for field, name in [('p2o5', 'P2O5'), ('cao', 'CaO'), ('mgo', 'MgO'), ('sio2', 'SiO2')]:
            value = self.vars[field].get().strip()
            if value:
                try:
                    percentage = float(value)
                    if percentage < 0 or percentage > 100:
                        errors.append(f"{name} doit être entre 0 et 100%")
                except ValueError:
                    errors.append(f"{name} doit être un nombre")
        
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return False
        
        return True
    
    def save_data(self):
        """Sauvegarder la réception"""
        # Récupérer les valeurs
        numero_voyage = int(self.vars['numero_voyage'].get())
        date_reception = datetime.strptime(self.vars['date_reception'].get(), '%Y-%m-%d').date()
        tonnage = float(self.vars['tonnage'].get())
        
        # Valeurs optionnelles
        numero_bl = self.vars['numero_bl'].get().strip() or None
        serie_camion = self.vars['serie_camion'].get().strip() or None
        observations = self.vars['observations'].get().strip() or None
        
        # Analyses chimiques
        p2o5 = float(self.vars['p2o5'].get()) if self.vars['p2o5'].get().strip() else None
        cao = float(self.vars['cao'].get()) if self.vars['cao'].get().strip() else None
        mgo = float(self.vars['mgo'].get()) if self.vars['mgo'].get().strip() else None
        sio2 = float(self.vars['sio2'].get()) if self.vars['sio2'].get().strip() else None
        
        # Sauvegarder
        reception_id = ReceptionPhosphateManager.ajouter_reception(
            numero_voyage, date_reception, tonnage, numero_bl, serie_camion,
            p2o5, cao, mgo, sio2, observations
        )
        
        return reception_id

class ProductionDialog(BaseDialog):
    """Dialogue pour ajouter/modifier une production"""
    
    def __init__(self, parent, production_data=None):
        self.production_data = production_data
        self.is_edit = production_data is not None
        
        title = "✏️ Modifier Production" if self.is_edit else "➕ Nouvelle Production"
        super().__init__(parent, title)
    
    def create_widgets(self):
        """Créer l'interface de production"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Titre
        title_text = "Modifier la production" if self.is_edit else "Ajouter une nouvelle production"
        title_label = ttk.Label(main_frame, text=title_text, font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Variables
        self.vars['date_production'] = tk.StringVar()
        self.vars['regime_travail'] = tk.StringVar()
        self.vars['total_reception'] = tk.StringVar()
        self.vars['total_production'] = tk.StringVar()
        self.vars['heures_marche'] = tk.StringVar()
        self.vars['consommation_elec'] = tk.StringVar()
        self.vars['consommation_eau'] = tk.StringVar()
        
        row = 1
        
        # Date de production
        ttk.Label(main_frame, text="Date de production *:").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['date_production'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Régime de travail
        ttk.Label(main_frame, text="Régime de travail (h):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['regime_travail'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Total réception
        ttk.Label(main_frame, text="Total réception (T):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['total_reception'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Total production
        ttk.Label(main_frame, text="Total production (T):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['total_production'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Heures de marche
        ttk.Label(main_frame, text="Heures de marche:").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['heures_marche'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Consommation électricité
        ttk.Label(main_frame, text="Consommation élec. (kWh):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['consommation_elec'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Consommation eau
        ttk.Label(main_frame, text="Consommation eau (m³):").grid(row=row, column=0, sticky=tk.W, pady=2)
        ttk.Entry(main_frame, textvariable=self.vars['consommation_eau'], width=30).grid(row=row, column=1, sticky=tk.W, pady=2)
        row += 1
        
        # Boutons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row+1, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="💾 Sauvegarder", command=self.on_ok).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="❌ Annuler", command=self.on_cancel).pack(side=tk.LEFT)
        
        # Valeurs par défaut
        self.vars['date_production'].set(datetime.now().strftime('%Y-%m-%d'))
        self.vars['regime_travail'].set('8')
    
    def validate_data(self):
        """Valider les données de production"""
        errors = []
        
        # Date
        try:
            datetime.strptime(self.vars['date_production'].get(), '%Y-%m-%d')
        except ValueError:
            errors.append("La date doit être au format YYYY-MM-DD")
        
        # Régime de travail
        try:
            regime = int(self.vars['regime_travail'].get())
            if regime <= 0 or regime > 24:
                errors.append("Le régime de travail doit être entre 1 et 24 heures")
        except ValueError:
            errors.append("Le régime de travail doit être un nombre entier")
        
        if errors:
            messagebox.showerror("Erreurs de validation", "\n".join(errors))
            return False
        
        return True
    
    def save_data(self):
        """Sauvegarder la production"""
        date_production = datetime.strptime(self.vars['date_production'].get(), '%Y-%m-%d').date()
        regime_travail = int(self.vars['regime_travail'].get())
        
        # Valeurs optionnelles
        total_reception = float(self.vars['total_reception'].get()) if self.vars['total_reception'].get().strip() else None
        total_production = float(self.vars['total_production'].get()) if self.vars['total_production'].get().strip() else None
        heures_marche = float(self.vars['heures_marche'].get()) if self.vars['heures_marche'].get().strip() else None
        consommation_elec = float(self.vars['consommation_elec'].get()) if self.vars['consommation_elec'].get().strip() else None
        consommation_eau = float(self.vars['consommation_eau'].get()) if self.vars['consommation_eau'].get().strip() else None
        
        production_id = ProductionManager.ajouter_production(
            date_production, regime_travail, total_reception, total_production,
            heures_marche, consommation_elec, consommation_eau
        )
        
        return production_id

def show_reception_dialog(parent, reception_data=None):
    """Afficher le dialogue de réception"""
    dialog = ReceptionDialog(parent, reception_data)
    parent.wait_window(dialog.dialog)
    return dialog.result

def show_production_dialog(parent, production_data=None):
    """Afficher le dialogue de production"""
    dialog = ProductionDialog(parent, production_data)
    parent.wait_window(dialog.dialog)
    return dialog.result
