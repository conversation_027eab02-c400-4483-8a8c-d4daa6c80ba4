#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de thèmes professionnel pour SOTRAMINE
Design moderne et interface utilisateur avancée
"""

import tkinter as tk
from tkinter import ttk

class ThemeManager:
    """Gestionnaire de thèmes professionnel"""
    
    def __init__(self):
        self.current_theme = "corporate_blue"
        self.themes = {
            "corporate_blue": {
                "name": "Corporate Blue",
                "description": "Thème professionnel bleu moderne",
                "colors": {
                    "primary": "#1e3a8a",      # Bleu professionnel
                    "secondary": "#3b82f6",    # Bleu clair
                    "accent": "#06b6d4",       # Cyan
                    "success": "#10b981",      # Vert
                    "warning": "#f59e0b",      # Orange
                    "danger": "#ef4444",       # Rouge
                    "info": "#3b82f6",         # Bleu info
                    "light": "#f8fafc",        # <PERSON><PERSON> tr<PERSON> clair
                    "dark": "#1e293b",         # <PERSON><PERSON> foncé
                    "background": "#ffffff",   # Blanc
                    "surface": "#f1f5f9",      # Gris surface
                    "border": "#e2e8f0",       # Gris bordure
                    "text_primary": "#1e293b", # Texte principal
                    "text_secondary": "#64748b", # Texte secondaire
                    "text_muted": "#94a3b8"    # Texte atténué
                },
                "fonts": {
                    "title": ("Segoe UI", 18, "bold"),
                    "heading": ("Segoe UI", 14, "bold"),
                    "subheading": ("Segoe UI", 12, "bold"),
                    "body": ("Segoe UI", 10),
                    "small": ("Segoe UI", 9),
                    "caption": ("Segoe UI", 8)
                },
                "spacing": {
                    "xs": 4,
                    "sm": 8,
                    "md": 16,
                    "lg": 24,
                    "xl": 32
                }
            },
            
            "modern_dark": {
                "name": "Modern Dark",
                "description": "Thème sombre moderne",
                "colors": {
                    "primary": "#6366f1",      # Indigo
                    "secondary": "#8b5cf6",    # Violet
                    "accent": "#06b6d4",       # Cyan
                    "success": "#10b981",      # Vert
                    "warning": "#f59e0b",      # Orange
                    "danger": "#ef4444",       # Rouge
                    "info": "#3b82f6",         # Bleu
                    "light": "#374151",        # Gris clair (dark mode)
                    "dark": "#111827",         # Très sombre
                    "background": "#1f2937",   # Fond sombre
                    "surface": "#374151",      # Surface sombre
                    "border": "#4b5563",       # Bordure sombre
                    "text_primary": "#f9fafb", # Texte clair
                    "text_secondary": "#d1d5db", # Texte secondaire clair
                    "text_muted": "#9ca3af"    # Texte atténué clair
                },
                "fonts": {
                    "title": ("Segoe UI", 18, "bold"),
                    "heading": ("Segoe UI", 14, "bold"),
                    "subheading": ("Segoe UI", 12, "bold"),
                    "body": ("Segoe UI", 10),
                    "small": ("Segoe UI", 9),
                    "caption": ("Segoe UI", 8)
                },
                "spacing": {
                    "xs": 4,
                    "sm": 8,
                    "md": 16,
                    "lg": 24,
                    "xl": 32
                }
            },
            
            "elegant_green": {
                "name": "Elegant Green",
                "description": "Thème vert élégant",
                "colors": {
                    "primary": "#059669",      # Vert émeraude
                    "secondary": "#10b981",    # Vert clair
                    "accent": "#06b6d4",       # Cyan
                    "success": "#10b981",      # Vert
                    "warning": "#f59e0b",      # Orange
                    "danger": "#ef4444",       # Rouge
                    "info": "#3b82f6",         # Bleu
                    "light": "#f0fdf4",        # Vert très clair
                    "dark": "#064e3b",         # Vert foncé
                    "background": "#ffffff",   # Blanc
                    "surface": "#ecfdf5",      # Vert surface
                    "border": "#d1fae5",       # Vert bordure
                    "text_primary": "#064e3b", # Texte vert foncé
                    "text_secondary": "#047857", # Texte vert
                    "text_muted": "#6b7280"    # Texte gris
                },
                "fonts": {
                    "title": ("Segoe UI", 18, "bold"),
                    "heading": ("Segoe UI", 14, "bold"),
                    "subheading": ("Segoe UI", 12, "bold"),
                    "body": ("Segoe UI", 10),
                    "small": ("Segoe UI", 9),
                    "caption": ("Segoe UI", 8)
                },
                "spacing": {
                    "xs": 4,
                    "sm": 8,
                    "md": 16,
                    "lg": 24,
                    "xl": 32
                }
            }
        }
    
    def get_theme(self, theme_name=None):
        """Obtenir un thème"""
        if theme_name is None:
            theme_name = self.current_theme
        return self.themes.get(theme_name, self.themes["corporate_blue"])
    
    def set_theme(self, theme_name):
        """Définir le thème actuel"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            return True
        return False
    
    def get_color(self, color_name, theme_name=None):
        """Obtenir une couleur du thème"""
        theme = self.get_theme(theme_name)
        return theme["colors"].get(color_name, "#000000")
    
    def get_font(self, font_name, theme_name=None):
        """Obtenir une police du thème"""
        theme = self.get_theme(theme_name)
        return theme["fonts"].get(font_name, ("Arial", 10))
    
    def get_spacing(self, size, theme_name=None):
        """Obtenir un espacement du thème"""
        theme = self.get_theme(theme_name)
        return theme["spacing"].get(size, 8)
    
    def apply_theme_to_widget(self, widget, widget_type="default"):
        """Appliquer le thème à un widget"""
        theme = self.get_theme()
        colors = theme["colors"]
        
        try:
            if widget_type == "frame":
                widget.configure(bg=colors["background"])
            elif widget_type == "label":
                widget.configure(
                    bg=colors["background"],
                    fg=colors["text_primary"],
                    font=theme["fonts"]["body"]
                )
            elif widget_type == "title":
                widget.configure(
                    bg=colors["background"],
                    fg=colors["primary"],
                    font=theme["fonts"]["title"]
                )
            elif widget_type == "heading":
                widget.configure(
                    bg=colors["background"],
                    fg=colors["text_primary"],
                    font=theme["fonts"]["heading"]
                )
            elif widget_type == "button":
                widget.configure(
                    bg=colors["primary"],
                    fg="white",
                    font=theme["fonts"]["body"],
                    relief="flat",
                    borderwidth=0,
                    padx=theme["spacing"]["md"],
                    pady=theme["spacing"]["sm"]
                )
            elif widget_type == "entry":
                widget.configure(
                    bg=colors["background"],
                    fg=colors["text_primary"],
                    font=theme["fonts"]["body"],
                    relief="solid",
                    borderwidth=1,
                    highlightthickness=1,
                    highlightcolor=colors["primary"]
                )
        except Exception as e:
            print(f"Erreur lors de l'application du thème: {e}")

class ModernStyles:
    """Styles modernes pour ttk widgets"""
    
    def __init__(self, theme_manager):
        self.theme_manager = theme_manager
        self.style = ttk.Style()
    
    def configure_styles(self):
        """Configurer tous les styles modernes"""
        theme = self.theme_manager.get_theme()
        colors = theme["colors"]
        fonts = theme["fonts"]
        
        # Style pour les boutons
        self.style.configure(
            "Modern.TButton",
            background=colors["primary"],
            foreground="white",
            font=fonts["body"],
            borderwidth=0,
            focuscolor="none",
            padding=(16, 8)
        )
        
        self.style.map(
            "Modern.TButton",
            background=[
                ("active", colors["secondary"]),
                ("pressed", colors["dark"])
            ]
        )
        
        # Style pour les boutons de succès
        self.style.configure(
            "Success.TButton",
            background=colors["success"],
            foreground="white",
            font=fonts["body"],
            borderwidth=0,
            focuscolor="none",
            padding=(16, 8)
        )
        
        # Style pour les boutons de danger
        self.style.configure(
            "Danger.TButton",
            background=colors["danger"],
            foreground="white",
            font=fonts["body"],
            borderwidth=0,
            focuscolor="none",
            padding=(16, 8)
        )
        
        # Style pour les labels de titre
        self.style.configure(
            "Title.TLabel",
            background=colors["background"],
            foreground=colors["primary"],
            font=fonts["title"]
        )
        
        # Style pour les labels d'en-tête
        self.style.configure(
            "Heading.TLabel",
            background=colors["background"],
            foreground=colors["text_primary"],
            font=fonts["heading"]
        )
        
        # Style pour les frames
        self.style.configure(
            "Modern.TFrame",
            background=colors["background"],
            relief="flat",
            borderwidth=0
        )
        
        # Style pour les LabelFrame
        self.style.configure(
            "Modern.TLabelframe",
            background=colors["background"],
            foreground=colors["text_primary"],
            font=fonts["subheading"],
            borderwidth=1,
            relief="solid"
        )
        
        # Style pour les Entry
        self.style.configure(
            "Modern.TEntry",
            fieldbackground=colors["background"],
            foreground=colors["text_primary"],
            font=fonts["body"],
            borderwidth=1,
            relief="solid",
            insertcolor=colors["primary"]
        )
        
        # Style pour les Combobox
        self.style.configure(
            "Modern.TCombobox",
            fieldbackground=colors["background"],
            foreground=colors["text_primary"],
            font=fonts["body"],
            borderwidth=1,
            relief="solid"
        )
        
        # Style pour les Treeview
        self.style.configure(
            "Modern.Treeview",
            background=colors["background"],
            foreground=colors["text_primary"],
            font=fonts["body"],
            fieldbackground=colors["surface"],
            borderwidth=1,
            relief="solid"
        )
        
        self.style.configure(
            "Modern.Treeview.Heading",
            background=colors["primary"],
            foreground="white",
            font=fonts["subheading"],
            relief="flat"
        )
        
        # Style pour les Notebook
        self.style.configure(
            "Modern.TNotebook",
            background=colors["background"],
            borderwidth=0
        )
        
        self.style.configure(
            "Modern.TNotebook.Tab",
            background=colors["surface"],
            foreground=colors["text_primary"],
            font=fonts["body"],
            padding=(16, 8),
            borderwidth=1,
            relief="solid"
        )
        
        self.style.map(
            "Modern.TNotebook.Tab",
            background=[
                ("selected", colors["primary"]),
                ("active", colors["secondary"])
            ],
            foreground=[
                ("selected", "white"),
                ("active", "white")
            ]
        )

class CardWidget:
    """Widget carte moderne"""
    
    def __init__(self, parent, theme_manager, title="", content="", icon=""):
        self.parent = parent
        self.theme_manager = theme_manager
        self.theme = theme_manager.get_theme()
        
        # Frame principale de la carte
        self.card_frame = tk.Frame(
            parent,
            bg=self.theme["colors"]["background"],
            relief="solid",
            borderwidth=1,
            bd=1
        )
        
        # Effet d'ombre (simulation)
        self.shadow_frame = tk.Frame(
            parent,
            bg=self.theme["colors"]["border"],
            height=2
        )
        
        # En-tête de la carte
        if title or icon:
            self.header_frame = tk.Frame(
                self.card_frame,
                bg=self.theme["colors"]["surface"],
                height=50
            )
            self.header_frame.pack(fill=tk.X, padx=1, pady=(1, 0))
            
            if icon:
                self.icon_label = tk.Label(
                    self.header_frame,
                    text=icon,
                    bg=self.theme["colors"]["surface"],
                    fg=self.theme["colors"]["primary"],
                    font=("Segoe UI", 16)
                )
                self.icon_label.pack(side=tk.LEFT, padx=16, pady=12)
            
            if title:
                self.title_label = tk.Label(
                    self.header_frame,
                    text=title,
                    bg=self.theme["colors"]["surface"],
                    fg=self.theme["colors"]["text_primary"],
                    font=self.theme["fonts"]["heading"]
                )
                self.title_label.pack(side=tk.LEFT, pady=12)
        
        # Contenu de la carte
        self.content_frame = tk.Frame(
            self.card_frame,
            bg=self.theme["colors"]["background"]
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True, padx=16, pady=16)
        
        if content:
            self.content_label = tk.Label(
                self.content_frame,
                text=content,
                bg=self.theme["colors"]["background"],
                fg=self.theme["colors"]["text_primary"],
                font=self.theme["fonts"]["body"],
                justify=tk.LEFT,
                wraplength=300
            )
            self.content_label.pack(anchor=tk.W)
    
    def pack(self, **kwargs):
        """Pack la carte avec effet d'ombre"""
        self.shadow_frame.pack(**kwargs)
        self.card_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """Grid la carte avec effet d'ombre"""
        shadow_kwargs = kwargs.copy()
        shadow_kwargs['row'] = kwargs.get('row', 0) + 1
        shadow_kwargs['sticky'] = 'ew'
        
        self.shadow_frame.grid(**shadow_kwargs)
        self.card_frame.grid(**kwargs)

class StatusBar:
    """Barre de statut moderne"""
    
    def __init__(self, parent, theme_manager):
        self.parent = parent
        self.theme_manager = theme_manager
        self.theme = theme_manager.get_theme()
        
        # Frame de la barre de statut
        self.status_frame = tk.Frame(
            parent,
            bg=self.theme["colors"]["surface"],
            height=30,
            relief="solid",
            borderwidth=1
        )
        
        # Variables de statut
        self.status_var = tk.StringVar(value="✅ Prêt")
        self.time_var = tk.StringVar()
        
        # Label de statut principal
        self.status_label = tk.Label(
            self.status_frame,
            textvariable=self.status_var,
            bg=self.theme["colors"]["surface"],
            fg=self.theme["colors"]["text_primary"],
            font=self.theme["fonts"]["small"]
        )
        self.status_label.pack(side=tk.LEFT, padx=16, pady=6)
        
        # Séparateur
        self.separator = tk.Frame(
            self.status_frame,
            bg=self.theme["colors"]["border"],
            width=1,
            height=20
        )
        self.separator.pack(side=tk.LEFT, padx=8, pady=5)
        
        # Label de temps
        self.time_label = tk.Label(
            self.status_frame,
            textvariable=self.time_var,
            bg=self.theme["colors"]["surface"],
            fg=self.theme["colors"]["text_secondary"],
            font=self.theme["fonts"]["small"]
        )
        self.time_label.pack(side=tk.RIGHT, padx=16, pady=6)
        
        # Mettre à jour l'heure
        self.update_time()
    
    def pack(self, **kwargs):
        """Pack la barre de statut"""
        self.status_frame.pack(**kwargs)
    
    def set_status(self, message, status_type="info"):
        """Définir le message de statut"""
        icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌",
            "loading": "🔄"
        }
        
        icon = icons.get(status_type, "ℹ️")
        self.status_var.set(f"{icon} {message}")
    
    def update_time(self):
        """Mettre à jour l'heure"""
        from datetime import datetime
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_var.set(current_time)
        
        # Programmer la prochaine mise à jour
        self.parent.after(1000, self.update_time)

# Instance globale du gestionnaire de thèmes
theme_manager = ThemeManager()
modern_styles = None

def initialize_theme_system(root):
    """Initialiser le système de thèmes"""
    global modern_styles
    modern_styles = ModernStyles(theme_manager)
    modern_styles.configure_styles()
    
    # Appliquer le thème à la fenêtre principale
    theme = theme_manager.get_theme()
    root.configure(bg=theme["colors"]["background"])

def get_theme_manager():
    """Obtenir le gestionnaire de thèmes"""
    return theme_manager

def get_modern_styles():
    """Obtenir les styles modernes"""
    return modern_styles
