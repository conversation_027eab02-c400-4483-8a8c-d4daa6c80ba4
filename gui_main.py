#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface graphique principale pour l'application SOTRAMINE
Interface professionnelle avec tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, date
from typing import Optional

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database_models import db_manager
from data_managers import (
    ReceptionPhosphateManager, ProductionManager, ArretManager,
    VenteManager, BilanManager
)
from config import get_config, get_app_info
from gui_dialogs import show_reception_dialog, show_production_dialog

class SotramineGUI:
    """Interface graphique principale pour SOTRAMINE"""

    def __init__(self):
        self.root = tk.Tk()
        self.status_var = tk.StringVar()  # Initialiser status_var en premier
        self.setup_main_window()
        self.create_menu()
        self.create_main_interface()
        self.load_initial_data()

    def setup_main_window(self):
        """Configuration de la fenêtre principale"""
        app_info = get_app_info()

        self.root.title(f"🏭 {app_info['name']} v{app_info['version']}")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Icône et style
        self.root.configure(bg='#f0f0f0')

        # Style ttk
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Couleurs personnalisées
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'light': '#F5F5F5',
            'dark': '#2C3E50'
        }

        # Configuration des styles
        self.style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground=self.colors['primary'])
        self.style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), foreground=self.colors['dark'])
        self.style.configure('Primary.TButton', font=('Arial', 10, 'bold'))

    def create_menu(self):
        """Créer la barre de menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📁 Fichier", menu=file_menu)
        file_menu.add_command(label="📊 Importer Excel", command=self.import_excel)
        file_menu.add_command(label="📤 Exporter données", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="🚪 Quitter", command=self.root.quit)

        # Menu Données
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📊 Données", menu=data_menu)
        data_menu.add_command(label="📦 Réceptions", command=lambda: self.show_module('reception'))
        data_menu.add_command(label="🏭 Production", command=lambda: self.show_module('production'))
        data_menu.add_command(label="⚠️ Arrêts", command=lambda: self.show_module('arrets'))
        data_menu.add_command(label="💰 Ventes", command=lambda: self.show_module('ventes'))

        # Menu Rapports
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📈 Rapports", menu=reports_menu)
        reports_menu.add_command(label="📊 Tableau de bord", command=self.show_dashboard)
        reports_menu.add_command(label="📋 Statistiques", command=self.show_statistics)
        reports_menu.add_command(label="📄 Rapport mensuel", command=self.generate_monthly_report)

        # Menu Aide
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="❓ Aide", menu=help_menu)
        help_menu.add_command(label="ℹ️ À propos", command=self.show_about)
        help_menu.add_command(label="📖 Documentation", command=self.show_help)

    def create_main_interface(self):
        """Créer l'interface principale"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Titre principal
        title_label = ttk.Label(main_frame, text="🏭 SOTRAMINE - Suivi Phosphate", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Panel de navigation (gauche)
        self.create_navigation_panel(main_frame)

        # Zone de contenu principal (droite)
        self.create_content_area(main_frame)

        # Barre de statut
        self.create_status_bar()

    def create_navigation_panel(self, parent):
        """Créer le panel de navigation"""
        nav_frame = ttk.LabelFrame(parent, text="📋 Navigation", padding="10")
        nav_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Boutons de navigation
        nav_buttons = [
            ("📊 Tableau de bord", self.show_dashboard),
            ("📦 Réceptions", lambda: self.show_module('reception')),
            ("🏭 Production", lambda: self.show_module('production')),
            ("⚠️ Arrêts", lambda: self.show_module('arrets')),
            ("💰 Ventes", lambda: self.show_module('ventes')),
            ("📈 Statistiques", self.show_statistics),
            ("📊 Import Excel", self.import_excel),
        ]

        for i, (text, command) in enumerate(nav_buttons):
            btn = ttk.Button(nav_frame, text=text, command=command, width=20)
            btn.grid(row=i, column=0, pady=2, sticky=(tk.W, tk.E))

        nav_frame.columnconfigure(0, weight=1)

    def create_content_area(self, parent):
        """Créer la zone de contenu principal"""
        self.content_frame = ttk.LabelFrame(parent, text="📄 Contenu", padding="10")
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)

        # Afficher le tableau de bord par défaut
        self.show_dashboard()

    def create_status_bar(self):
        """Créer la barre de statut"""
        self.status_var.set("✅ Prêt")

        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))

    def clear_content(self):
        """Vider la zone de contenu"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        """Afficher le tableau de bord"""
        self.clear_content()
        self.status_var.set("📊 Tableau de bord")

        # Titre
        title = ttk.Label(self.content_frame, text="📊 Tableau de Bord", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Frame pour les statistiques
        stats_frame = ttk.Frame(self.content_frame)
        stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        # Obtenir les statistiques
        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            productions = ProductionManager.obtenir_production()
            ventes = VenteManager.obtenir_ventes()

            # Cartes de statistiques
            self.create_stat_card(stats_frame, "📦 Réceptions", len(receptions), 0, 0)
            self.create_stat_card(stats_frame, "🏭 Productions", len(productions), 0, 1)
            self.create_stat_card(stats_frame, "💰 Ventes", len(ventes), 0, 2)

            # Calculer le tonnage total
            tonnage_total = sum(r.get('tonnage', 0) for r in receptions)
            self.create_stat_card(stats_frame, "⚖️ Tonnage Total", f"{tonnage_total:,.0f} T", 1, 0)

            # Dernière réception
            if receptions:
                derniere_reception = receptions[0]['date_reception']
                self.create_stat_card(stats_frame, "📅 Dernière Réception", derniere_reception, 1, 1)

            # État de la base de données
            self.create_stat_card(stats_frame, "💾 Base de Données", "✅ Connectée", 1, 2)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données: {e}")

        # Graphiques récents (placeholder)
        chart_frame = ttk.LabelFrame(self.content_frame, text="📈 Activité Récente", padding="10")
        chart_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Liste des activités récentes
        self.create_recent_activity(chart_frame)

    def create_stat_card(self, parent, title, value, row, col):
        """Créer une carte de statistique"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="10")
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky=(tk.W, tk.E))

        value_label = ttk.Label(card_frame, text=str(value), font=('Arial', 14, 'bold'))
        value_label.grid(row=0, column=0)

        parent.columnconfigure(col, weight=1)

    def create_recent_activity(self, parent):
        """Créer la liste d'activité récente"""
        # Treeview pour afficher les données
        columns = ('Type', 'Date', 'Description', 'Valeur')
        tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)

        # Configuration des colonnes
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # Scrollbar
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # Placement
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)

        # Charger les données récentes
        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()[:5]
            for reception in receptions:
                tree.insert('', tk.END, values=(
                    '📦 Réception',
                    reception['date_reception'],
                    f"Voyage {reception['numero_voyage']}",
                    f"{reception['tonnage']} T"
                ))
        except Exception as e:
            tree.insert('', tk.END, values=('❌ Erreur', '', str(e), ''))

    def show_module(self, module_name):
        """Afficher un module spécifique"""
        self.clear_content()

        if module_name == 'reception':
            self.show_reception_module()
        elif module_name == 'production':
            self.show_production_module()
        elif module_name == 'arrets':
            self.show_arrets_module()
        elif module_name == 'ventes':
            self.show_ventes_module()

    def show_reception_module(self):
        """Afficher le module de réception"""
        self.status_var.set("📦 Module Réceptions")

        # Titre
        title = ttk.Label(self.content_frame, text="📦 Gestion des Réceptions", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Boutons d'action
        btn_frame = ttk.Frame(self.content_frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="➕ Nouvelle Réception", command=self.add_reception).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📊 Statistiques", command=self.show_reception_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🔄 Actualiser", command=self.refresh_receptions).pack(side=tk.LEFT)

        # Table des réceptions
        self.create_reception_table()

    def create_reception_table(self):
        """Créer la table des réceptions"""
        # Frame pour la table
        table_frame = ttk.Frame(self.content_frame)
        table_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Colonnes
        columns = ('ID', 'Voyage', 'Date', 'Tonnage', 'P2O5%', 'Observations')
        self.reception_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        widths = [50, 80, 100, 100, 80, 200]
        for i, col in enumerate(columns):
            self.reception_tree.heading(col, text=col)
            self.reception_tree.column(col, width=widths[i])

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.reception_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.reception_tree.xview)
        self.reception_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Placement
        self.reception_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        self.content_frame.rowconfigure(2, weight=1)

        # Événement double-clic pour modifier
        self.reception_tree.bind('<Double-1>', self.on_reception_double_click)

        # Charger les données
        self.refresh_receptions()

    def refresh_receptions(self):
        """Actualiser la liste des réceptions"""
        # Vider la table
        for item in self.reception_tree.get_children():
            self.reception_tree.delete(item)

        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            for reception in receptions[:50]:  # Limiter à 50 pour la performance
                self.reception_tree.insert('', tk.END, values=(
                    reception['id'],
                    reception['numero_voyage'],
                    reception['date_reception'],
                    f"{reception['tonnage']:.1f}" if reception['tonnage'] else '',
                    f"{reception['p2o5_pourcentage']:.1f}" if reception['p2o5_pourcentage'] else '',
                    reception['observations'] or ''
                ))

            self.status_var.set(f"📦 {len(receptions)} réceptions chargées")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")

    def load_initial_data(self):
        """Charger les données initiales"""
        try:
            # Vérifier la connexion à la base de données
            tables = db_manager.get_all_tables()
            self.status_var.set(f"✅ Base de données connectée ({len(tables)} tables)")
        except Exception as e:
            messagebox.showerror("Erreur de base de données", f"Impossible de se connecter à la base de données: {e}")

    def add_reception(self):
        """Ajouter une nouvelle réception"""
        result = show_reception_dialog(self.root)
        if result:
            messagebox.showinfo("Succès", f"Réception ajoutée avec l'ID: {result}")
            self.refresh_receptions()

    def on_reception_double_click(self, event):
        """Gestionnaire de double-clic sur une réception"""
        selection = self.reception_tree.selection()
        if selection:
            item = self.reception_tree.item(selection[0])
            reception_id = item['values'][0]

            # Récupérer les données complètes de la réception
            try:
                receptions = ReceptionPhosphateManager.obtenir_receptions()
                reception_data = next((r for r in receptions if r['id'] == reception_id), None)

                if reception_data:
                    result = show_reception_dialog(self.root, reception_data)
                    if result:
                        messagebox.showinfo("Succès", "Réception modifiée avec succès")
                        self.refresh_receptions()
                else:
                    messagebox.showerror("Erreur", "Réception non trouvée")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {e}")

    def show_reception_stats(self):
        """Afficher les statistiques des réceptions"""
        self.clear_content()
        self.status_var.set("📈 Statistiques Réceptions")

        # Titre
        title = ttk.Label(self.content_frame, text="📈 Statistiques des Réceptions", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        try:
            # Obtenir les statistiques générales
            receptions = ReceptionPhosphateManager.obtenir_receptions()

            if not receptions:
                ttk.Label(self.content_frame, text="Aucune donnée disponible").grid(row=1, column=0)
                return

            # Frame pour les statistiques
            stats_frame = ttk.LabelFrame(self.content_frame, text="📊 Statistiques Générales", padding="10")
            stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

            # Calculer les statistiques
            total_receptions = len(receptions)
            tonnage_total = sum(r.get('tonnage', 0) for r in receptions if r.get('tonnage'))
            tonnage_moyen = tonnage_total / total_receptions if total_receptions > 0 else 0

            # Analyses chimiques moyennes
            p2o5_values = [r.get('p2o5_pourcentage') for r in receptions if r.get('p2o5_pourcentage')]
            p2o5_moyen = sum(p2o5_values) / len(p2o5_values) if p2o5_values else 0

            # Afficher les statistiques
            stats_data = [
                ("Nombre total de réceptions", f"{total_receptions:,}"),
                ("Tonnage total", f"{tonnage_total:,.1f} T"),
                ("Tonnage moyen par réception", f"{tonnage_moyen:,.1f} T"),
                ("P2O5 moyen", f"{p2o5_moyen:.2f}%" if p2o5_moyen > 0 else "N/A"),
            ]

            for i, (label, value) in enumerate(stats_data):
                ttk.Label(stats_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
                ttk.Label(stats_frame, text=value, font=('Arial', 10, 'bold')).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)

            # Graphique par mois (simulation)
            chart_frame = ttk.LabelFrame(self.content_frame, text="📈 Répartition Mensuelle", padding="10")
            chart_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

            # Créer un tableau simple pour les données mensuelles
            monthly_tree = ttk.Treeview(chart_frame, columns=('Mois', 'Réceptions', 'Tonnage'), show='headings', height=8)
            monthly_tree.heading('Mois', text='Mois')
            monthly_tree.heading('Réceptions', text='Réceptions')
            monthly_tree.heading('Tonnage', text='Tonnage (T)')

            # Calculer les données mensuelles
            monthly_data = {}
            for reception in receptions:
                date_str = reception.get('date_reception', '')
                if date_str:
                    try:
                        date_obj = datetime.strptime(str(date_str), '%Y-%m-%d')
                        month_key = date_obj.strftime('%Y-%m')

                        if month_key not in monthly_data:
                            monthly_data[month_key] = {'count': 0, 'tonnage': 0}

                        monthly_data[month_key]['count'] += 1
                        monthly_data[month_key]['tonnage'] += reception.get('tonnage', 0)
                    except:
                        continue

            # Afficher les données mensuelles
            for month, data in sorted(monthly_data.items()):
                monthly_tree.insert('', tk.END, values=(
                    month,
                    data['count'],
                    f"{data['tonnage']:,.1f}"
                ))

            monthly_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            chart_frame.columnconfigure(0, weight=1)
            chart_frame.rowconfigure(0, weight=1)
            self.content_frame.rowconfigure(2, weight=1)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du calcul des statistiques: {e}")

    def show_production_module(self):
        """Afficher le module de production"""
        self.clear_content()
        self.status_var.set("🏭 Module Production")

        # Titre
        title = ttk.Label(self.content_frame, text="🏭 Gestion de la Production", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Boutons d'action
        btn_frame = ttk.Frame(self.content_frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="➕ Nouvelle Production", command=self.add_production).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📊 Rendements", command=self.show_production_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🔄 Actualiser", command=self.refresh_production).pack(side=tk.LEFT)

        # Table de production
        self.create_production_table()

    def create_production_table(self):
        """Créer la table de production"""
        # Frame pour la table
        table_frame = ttk.Frame(self.content_frame)
        table_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Colonnes
        columns = ('ID', 'Date', 'Régime', 'Réception (T)', 'Production (T)', 'Rendement (%)', 'Heures Marche')
        self.production_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        widths = [50, 100, 80, 120, 120, 100, 120]
        for i, col in enumerate(columns):
            self.production_tree.heading(col, text=col)
            self.production_tree.column(col, width=widths[i])

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.production_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.production_tree.xview)
        self.production_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Placement
        self.production_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        self.content_frame.rowconfigure(2, weight=1)

        # Événement double-clic pour modifier
        self.production_tree.bind('<Double-1>', self.on_production_double_click)

        # Charger les données
        self.refresh_production()

    def refresh_production(self):
        """Actualiser la liste de production"""
        # Vider la table
        for item in self.production_tree.get_children():
            self.production_tree.delete(item)

        try:
            productions = ProductionManager.obtenir_production()
            for prod in productions[:50]:  # Limiter à 50 pour la performance
                # Calculer le rendement
                rendement = ""
                if prod.get('total_reception_phosphate_brut') and prod.get('total_production_concentre'):
                    rendement_val = (prod['total_production_concentre'] / prod['total_reception_phosphate_brut']) * 100
                    rendement = f"{rendement_val:.1f}"

                self.production_tree.insert('', tk.END, values=(
                    prod['id'],
                    prod['date_production'],
                    f"{prod['regime_travail']}h" if prod['regime_travail'] else '',
                    f"{prod['total_reception_phosphate_brut']:.1f}" if prod['total_reception_phosphate_brut'] else '',
                    f"{prod['total_production_concentre']:.1f}" if prod['total_production_concentre'] else '',
                    rendement,
                    f"{prod['total_heures_marche']:.1f}" if prod['total_heures_marche'] else ''
                ))

            self.status_var.set(f"🏭 {len(productions)} productions chargées")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")

    def add_production(self):
        """Ajouter une nouvelle production"""
        result = show_production_dialog(self.root)
        if result:
            messagebox.showinfo("Succès", f"Production ajoutée avec l'ID: {result}")
            self.refresh_production()

    def on_production_double_click(self, event):
        """Gestionnaire de double-clic sur une production"""
        selection = self.production_tree.selection()
        if selection:
            item = self.production_tree.item(selection[0])
            production_id = item['values'][0]
            messagebox.showinfo("Modification", f"Modification de la production ID: {production_id}\n(Fonctionnalité en développement)")

    def show_production_stats(self):
        """Afficher les statistiques de production"""
        messagebox.showinfo("Statistiques", "Statistiques de production en cours de développement")

    def show_arrets_module(self):
        """Afficher le module des arrêts"""
        self.status_var.set("⚠️ Module Arrêts")
        title = ttk.Label(self.content_frame, text="⚠️ Module Arrêts", style='Heading.TLabel')
        title.grid(row=0, column=0, pady=20)

        info_label = ttk.Label(self.content_frame, text="Module des arrêts en cours de développement...")
        info_label.grid(row=1, column=0, pady=20)

    def show_ventes_module(self):
        """Afficher le module des ventes"""
        self.status_var.set("💰 Module Ventes")
        title = ttk.Label(self.content_frame, text="💰 Module Ventes", style='Heading.TLabel')
        title.grid(row=0, column=0, pady=20)

        info_label = ttk.Label(self.content_frame, text="Module des ventes en cours de développement...")
        info_label.grid(row=1, column=0, pady=20)

    def show_statistics(self):
        """Afficher les statistiques"""
        self.status_var.set("📈 Statistiques")
        messagebox.showinfo("En développement", "Module de statistiques en cours de développement")

    def import_excel(self):
        """Importer un fichier Excel"""
        file_path = filedialog.askopenfilename(
            title="Sélectionner un fichier Excel",
            filetypes=[("Fichiers Excel", "*.xlsx *.xls"), ("Tous les fichiers", "*.*")]
        )

        if file_path:
            # Confirmer l'import
            response = messagebox.askyesno(
                "Confirmer l'import",
                f"Voulez-vous importer les données du fichier:\n{os.path.basename(file_path)}\n\nCela peut prendre quelques minutes."
            )

            if response:
                try:
                    # Afficher une fenêtre de progression
                    progress_window = tk.Toplevel(self.root)
                    progress_window.title("Import en cours...")
                    progress_window.geometry("400x150")
                    progress_window.resizable(False, False)

                    # Centrer la fenêtre
                    progress_window.transient(self.root)
                    progress_window.grab_set()

                    ttk.Label(progress_window, text="Import des données Excel en cours...", font=('Arial', 12)).pack(pady=20)

                    progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
                    progress_bar.pack(pady=10, padx=20, fill=tk.X)
                    progress_bar.start()

                    status_label = ttk.Label(progress_window, text="Initialisation...")
                    status_label.pack(pady=10)

                    # Forcer la mise à jour de l'affichage
                    progress_window.update()

                    # Importer les données
                    from excel_importer import ExcelImporter

                    status_label.config(text="Lecture du fichier Excel...")
                    progress_window.update()

                    importer = ExcelImporter(file_path)

                    status_label.config(text="Import des données...")
                    progress_window.update()

                    stats = importer.importer_tout()

                    # Fermer la fenêtre de progression
                    progress_bar.stop()
                    progress_window.destroy()

                    # Afficher les résultats
                    total_imported = (stats['receptions'] + stats['productions'] +
                                    stats['arrets_laverie'] + stats['arrets_concentrateur'] + stats['ventes'])

                    result_message = f"""Import terminé avec succès !

📊 Résultats:
• Réceptions: {stats['receptions']}
• Productions: {stats['productions']}
• Arrêts laverie: {stats['arrets_laverie']}
• Arrêts concentrateur: {stats['arrets_concentrateur']}
• Ventes: {stats['ventes']}

Total: {total_imported} enregistrements importés"""

                    if stats['erreurs']:
                        result_message += f"\n\n⚠️ {len(stats['erreurs'])} erreur(s) détectée(s)"

                    messagebox.showinfo("Import terminé", result_message)

                    # Actualiser l'affichage si on est sur le tableau de bord
                    self.show_dashboard()

                except Exception as e:
                    # Fermer la fenêtre de progression en cas d'erreur
                    try:
                        progress_bar.stop()
                        progress_window.destroy()
                    except:
                        pass

                    messagebox.showerror("Erreur d'import", f"Erreur lors de l'import:\n{str(e)}")

    def export_data(self):
        """Exporter les données"""
        messagebox.showinfo("Export", "Fonctionnalité d'export en cours de développement")

    def generate_monthly_report(self):
        """Générer un rapport mensuel"""
        messagebox.showinfo("Rapport", "Génération de rapport en cours de développement")

    def show_about(self):
        """Afficher les informations sur l'application"""
        app_info = get_app_info()
        about_text = f"""
{app_info['name']} v{app_info['version']}

{app_info['description']}

Développé par: {app_info['author']}
Dernière mise à jour: {app_info['last_updated']}

Python: {app_info['python_version']}
Dépendances: {', '.join(app_info['dependencies'])}
        """
        messagebox.showinfo("À propos", about_text)

    def show_help(self):
        """Afficher l'aide"""
        help_text = """
🏭 AIDE SOTRAMINE

Navigation:
• Utilisez le menu de gauche pour naviguer entre les modules
• Le tableau de bord affiche un résumé des données
• Chaque module permet de gérer un type de données spécifique

Modules disponibles:
📦 Réceptions - Gestion des réceptions de phosphate
🏭 Production - Suivi de la production journalière
⚠️ Arrêts - Gestion des arrêts et maintenance
💰 Ventes - Planning et suivi des ventes

Fonctionnalités:
📊 Import Excel - Importer des données depuis Excel
📈 Statistiques - Analyses et rapports
📄 Export - Exporter les données

Pour plus d'aide, consultez le fichier README.md
        """
        messagebox.showinfo("Aide", help_text)

    def run(self):
        """Lancer l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    try:
        app = SotramineGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Erreur fatale", f"Erreur lors du lancement de l'application: {e}")

if __name__ == "__main__":
    main()
