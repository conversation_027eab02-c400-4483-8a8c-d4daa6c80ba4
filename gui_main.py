#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface graphique principale pour l'application SOTRAMINE
Interface professionnelle avec tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, date
from typing import Optional

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database_models import db_manager
from data_managers import (
    ReceptionPhosphateManager, ProductionManager, ArretManager,
    VenteManager, BilanManager
)
from config import get_config, get_app_info
from gui_dialogs import show_reception_dialog, show_production_dialog

class SotramineGUI:
    """Interface graphique principale pour SOTRAMINE"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_main_window()
        self.create_menu()
        self.create_main_interface()
        self.load_initial_data()

    def setup_main_window(self):
        """Configuration de la fenêtre principale"""
        app_info = get_app_info()

        self.root.title(f"🏭 {app_info['name']} v{app_info['version']}")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Icône et style
        self.root.configure(bg='#f0f0f0')

        # Style ttk
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # Couleurs personnalisées
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'warning': '#C73E1D',
            'light': '#F5F5F5',
            'dark': '#2C3E50'
        }

        # Configuration des styles
        self.style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground=self.colors['primary'])
        self.style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), foreground=self.colors['dark'])
        self.style.configure('Primary.TButton', font=('Arial', 10, 'bold'))

    def create_menu(self):
        """Créer la barre de menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📁 Fichier", menu=file_menu)
        file_menu.add_command(label="📊 Importer Excel", command=self.import_excel)
        file_menu.add_command(label="📤 Exporter données", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="🚪 Quitter", command=self.root.quit)

        # Menu Données
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📊 Données", menu=data_menu)
        data_menu.add_command(label="📦 Réceptions", command=lambda: self.show_module('reception'))
        data_menu.add_command(label="🏭 Production", command=lambda: self.show_module('production'))
        data_menu.add_command(label="⚠️ Arrêts", command=lambda: self.show_module('arrets'))
        data_menu.add_command(label="💰 Ventes", command=lambda: self.show_module('ventes'))

        # Menu Rapports
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📈 Rapports", menu=reports_menu)
        reports_menu.add_command(label="📊 Tableau de bord", command=self.show_dashboard)
        reports_menu.add_command(label="📋 Statistiques", command=self.show_statistics)
        reports_menu.add_command(label="📄 Rapport mensuel", command=self.generate_monthly_report)

        # Menu Aide
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="❓ Aide", menu=help_menu)
        help_menu.add_command(label="ℹ️ À propos", command=self.show_about)
        help_menu.add_command(label="📖 Documentation", command=self.show_help)

    def create_main_interface(self):
        """Créer l'interface principale"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # Titre principal
        title_label = ttk.Label(main_frame, text="🏭 SOTRAMINE - Suivi Phosphate", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Panel de navigation (gauche)
        self.create_navigation_panel(main_frame)

        # Zone de contenu principal (droite)
        self.create_content_area(main_frame)

        # Barre de statut
        self.create_status_bar()

    def create_navigation_panel(self, parent):
        """Créer le panel de navigation"""
        nav_frame = ttk.LabelFrame(parent, text="📋 Navigation", padding="10")
        nav_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Boutons de navigation
        nav_buttons = [
            ("📊 Tableau de bord", self.show_dashboard),
            ("📦 Réceptions", lambda: self.show_module('reception')),
            ("🏭 Production", lambda: self.show_module('production')),
            ("⚠️ Arrêts", lambda: self.show_module('arrets')),
            ("💰 Ventes", lambda: self.show_module('ventes')),
            ("📈 Statistiques", self.show_statistics),
            ("📊 Import Excel", self.import_excel),
        ]

        for i, (text, command) in enumerate(nav_buttons):
            btn = ttk.Button(nav_frame, text=text, command=command, width=20)
            btn.grid(row=i, column=0, pady=2, sticky=(tk.W, tk.E))

        nav_frame.columnconfigure(0, weight=1)

    def create_content_area(self, parent):
        """Créer la zone de contenu principal"""
        self.content_frame = ttk.LabelFrame(parent, text="📄 Contenu", padding="10")
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)

        # Afficher le tableau de bord par défaut
        self.show_dashboard()

    def create_status_bar(self):
        """Créer la barre de statut"""
        self.status_var = tk.StringVar()
        self.status_var.set("✅ Prêt")

        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))

    def clear_content(self):
        """Vider la zone de contenu"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        """Afficher le tableau de bord"""
        self.clear_content()
        self.status_var.set("📊 Tableau de bord")

        # Titre
        title = ttk.Label(self.content_frame, text="📊 Tableau de Bord", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Frame pour les statistiques
        stats_frame = ttk.Frame(self.content_frame)
        stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        # Obtenir les statistiques
        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            productions = ProductionManager.obtenir_production()
            ventes = VenteManager.obtenir_ventes()

            # Cartes de statistiques
            self.create_stat_card(stats_frame, "📦 Réceptions", len(receptions), 0, 0)
            self.create_stat_card(stats_frame, "🏭 Productions", len(productions), 0, 1)
            self.create_stat_card(stats_frame, "💰 Ventes", len(ventes), 0, 2)

            # Calculer le tonnage total
            tonnage_total = sum(r.get('tonnage', 0) for r in receptions)
            self.create_stat_card(stats_frame, "⚖️ Tonnage Total", f"{tonnage_total:,.0f} T", 1, 0)

            # Dernière réception
            if receptions:
                derniere_reception = receptions[0]['date_reception']
                self.create_stat_card(stats_frame, "📅 Dernière Réception", derniere_reception, 1, 1)

            # État de la base de données
            self.create_stat_card(stats_frame, "💾 Base de Données", "✅ Connectée", 1, 2)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données: {e}")

        # Graphiques récents (placeholder)
        chart_frame = ttk.LabelFrame(self.content_frame, text="📈 Activité Récente", padding="10")
        chart_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Liste des activités récentes
        self.create_recent_activity(chart_frame)

    def create_stat_card(self, parent, title, value, row, col):
        """Créer une carte de statistique"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="10")
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky=(tk.W, tk.E))

        value_label = ttk.Label(card_frame, text=str(value), font=('Arial', 14, 'bold'))
        value_label.grid(row=0, column=0)

        parent.columnconfigure(col, weight=1)

    def create_recent_activity(self, parent):
        """Créer la liste d'activité récente"""
        # Treeview pour afficher les données
        columns = ('Type', 'Date', 'Description', 'Valeur')
        tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)

        # Configuration des colonnes
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # Scrollbar
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # Placement
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)

        # Charger les données récentes
        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()[:5]
            for reception in receptions:
                tree.insert('', tk.END, values=(
                    '📦 Réception',
                    reception['date_reception'],
                    f"Voyage {reception['numero_voyage']}",
                    f"{reception['tonnage']} T"
                ))
        except Exception as e:
            tree.insert('', tk.END, values=('❌ Erreur', '', str(e), ''))

    def show_module(self, module_name):
        """Afficher un module spécifique"""
        self.clear_content()

        if module_name == 'reception':
            self.show_reception_module()
        elif module_name == 'production':
            self.show_production_module()
        elif module_name == 'arrets':
            self.show_arrets_module()
        elif module_name == 'ventes':
            self.show_ventes_module()

    def show_reception_module(self):
        """Afficher le module de réception"""
        self.status_var.set("📦 Module Réceptions")

        # Titre
        title = ttk.Label(self.content_frame, text="📦 Gestion des Réceptions", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Boutons d'action
        btn_frame = ttk.Frame(self.content_frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="➕ Nouvelle Réception", command=self.add_reception).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📊 Statistiques", command=self.show_reception_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🔄 Actualiser", command=self.refresh_receptions).pack(side=tk.LEFT)

        # Table des réceptions
        self.create_reception_table()

    def create_reception_table(self):
        """Créer la table des réceptions"""
        # Frame pour la table
        table_frame = ttk.Frame(self.content_frame)
        table_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Colonnes
        columns = ('ID', 'Voyage', 'Date', 'Tonnage', 'P2O5%', 'Observations')
        self.reception_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        widths = [50, 80, 100, 100, 80, 200]
        for i, col in enumerate(columns):
            self.reception_tree.heading(col, text=col)
            self.reception_tree.column(col, width=widths[i])

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.reception_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.reception_tree.xview)
        self.reception_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Placement
        self.reception_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        self.content_frame.rowconfigure(2, weight=1)

        # Événement double-clic pour modifier
        self.reception_tree.bind('<Double-1>', self.on_reception_double_click)

        # Charger les données
        self.refresh_receptions()

    def refresh_receptions(self):
        """Actualiser la liste des réceptions"""
        # Vider la table
        for item in self.reception_tree.get_children():
            self.reception_tree.delete(item)

        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            for reception in receptions[:50]:  # Limiter à 50 pour la performance
                self.reception_tree.insert('', tk.END, values=(
                    reception['id'],
                    reception['numero_voyage'],
                    reception['date_reception'],
                    f"{reception['tonnage']:.1f}" if reception['tonnage'] else '',
                    f"{reception['p2o5_pourcentage']:.1f}" if reception['p2o5_pourcentage'] else '',
                    reception['observations'] or ''
                ))

            self.status_var.set(f"📦 {len(receptions)} réceptions chargées")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")

    def load_initial_data(self):
        """Charger les données initiales"""
        try:
            # Vérifier la connexion à la base de données
            tables = db_manager.get_all_tables()
            self.status_var.set(f"✅ Base de données connectée ({len(tables)} tables)")
        except Exception as e:
            messagebox.showerror("Erreur de base de données", f"Impossible de se connecter à la base de données: {e}")

    def add_reception(self):
        """Ajouter une nouvelle réception"""
        result = show_reception_dialog(self.root)
        if result:
            messagebox.showinfo("Succès", f"Réception ajoutée avec l'ID: {result}")
            self.refresh_receptions()

    def on_reception_double_click(self, event):
        """Gestionnaire de double-clic sur une réception"""
        selection = self.reception_tree.selection()
        if selection:
            item = self.reception_tree.item(selection[0])
            reception_id = item['values'][0]

            # Récupérer les données complètes de la réception
            try:
                receptions = ReceptionPhosphateManager.obtenir_receptions()
                reception_data = next((r for r in receptions if r['id'] == reception_id), None)

                if reception_data:
                    result = show_reception_dialog(self.root, reception_data)
                    if result:
                        messagebox.showinfo("Succès", "Réception modifiée avec succès")
                        self.refresh_receptions()
                else:
                    messagebox.showerror("Erreur", "Réception non trouvée")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {e}")

    def show_reception_stats(self):
        """Afficher les statistiques des réceptions"""
        messagebox.showinfo("En développement", "Statistiques détaillées en cours de développement")

    def show_production_module(self):
        """Afficher le module de production"""
        self.status_var.set("🏭 Module Production")
        title = ttk.Label(self.content_frame, text="🏭 Module Production", style='Heading.TLabel')
        title.grid(row=0, column=0, pady=20)

        info_label = ttk.Label(self.content_frame, text="Module de production en cours de développement...")
        info_label.grid(row=1, column=0, pady=20)

    def show_arrets_module(self):
        """Afficher le module des arrêts"""
        self.status_var.set("⚠️ Module Arrêts")
        title = ttk.Label(self.content_frame, text="⚠️ Module Arrêts", style='Heading.TLabel')
        title.grid(row=0, column=0, pady=20)

        info_label = ttk.Label(self.content_frame, text="Module des arrêts en cours de développement...")
        info_label.grid(row=1, column=0, pady=20)

    def show_ventes_module(self):
        """Afficher le module des ventes"""
        self.status_var.set("💰 Module Ventes")
        title = ttk.Label(self.content_frame, text="💰 Module Ventes", style='Heading.TLabel')
        title.grid(row=0, column=0, pady=20)

        info_label = ttk.Label(self.content_frame, text="Module des ventes en cours de développement...")
        info_label.grid(row=1, column=0, pady=20)

    def show_statistics(self):
        """Afficher les statistiques"""
        self.status_var.set("📈 Statistiques")
        messagebox.showinfo("En développement", "Module de statistiques en cours de développement")

    def import_excel(self):
        """Importer un fichier Excel"""
        file_path = filedialog.askopenfilename(
            title="Sélectionner un fichier Excel",
            filetypes=[("Fichiers Excel", "*.xlsx *.xls"), ("Tous les fichiers", "*.*")]
        )

        if file_path:
            messagebox.showinfo("Import", f"Import du fichier: {os.path.basename(file_path)}\n(Fonctionnalité en développement)")

    def export_data(self):
        """Exporter les données"""
        messagebox.showinfo("Export", "Fonctionnalité d'export en cours de développement")

    def generate_monthly_report(self):
        """Générer un rapport mensuel"""
        messagebox.showinfo("Rapport", "Génération de rapport en cours de développement")

    def show_about(self):
        """Afficher les informations sur l'application"""
        app_info = get_app_info()
        about_text = f"""
{app_info['name']} v{app_info['version']}

{app_info['description']}

Développé par: {app_info['author']}
Dernière mise à jour: {app_info['last_updated']}

Python: {app_info['python_version']}
Dépendances: {', '.join(app_info['dependencies'])}
        """
        messagebox.showinfo("À propos", about_text)

    def show_help(self):
        """Afficher l'aide"""
        help_text = """
🏭 AIDE SOTRAMINE

Navigation:
• Utilisez le menu de gauche pour naviguer entre les modules
• Le tableau de bord affiche un résumé des données
• Chaque module permet de gérer un type de données spécifique

Modules disponibles:
📦 Réceptions - Gestion des réceptions de phosphate
🏭 Production - Suivi de la production journalière
⚠️ Arrêts - Gestion des arrêts et maintenance
💰 Ventes - Planning et suivi des ventes

Fonctionnalités:
📊 Import Excel - Importer des données depuis Excel
📈 Statistiques - Analyses et rapports
📄 Export - Exporter les données

Pour plus d'aide, consultez le fichier README.md
        """
        messagebox.showinfo("Aide", help_text)

    def run(self):
        """Lancer l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    try:
        app = SotramineGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Erreur fatale", f"Erreur lors du lancement de l'application: {e}")

if __name__ == "__main__":
    main()
