#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface graphique principale pour l'application SOTRAMINE
Interface professionnelle avec tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime, date
from typing import Optional

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database_models import db_manager
from data_managers import (
    ReceptionPhosphateManager, ProductionManager, ArretManager,
    VenteManager, BilanManager
)
from config import get_config, get_app_info
from gui_dialogs import show_reception_dialog, show_production_dialog
from config_manager import ConfigManager, ConfigGUI
from themes import (
    ThemeManager, ModernStyles, CardWidget, StatusBar,
    initialize_theme_system, get_theme_manager, get_modern_styles
)

class SotramineGUI:
    """Interface graphique principale pour SOTRAMINE"""

    def __init__(self):
        self.root = tk.Tk()
        self.status_var = tk.StringVar()  # Initialiser status_var en premier
        self.config_manager = ConfigManager()  # Gestionnaire de configuration

        # Initialiser le système de thèmes
        initialize_theme_system(self.root)
        self.theme_manager = get_theme_manager()
        self.modern_styles = get_modern_styles()

        self.setup_main_window()
        self.create_menu()
        self.create_main_interface()
        self.load_initial_data()

    def setup_main_window(self):
        """Configuration de la fenêtre principale"""
        app_info = get_app_info()

        # Utiliser la configuration pour la taille de fenêtre
        window_size = self.config_manager.get_value('ui', 'window_size', '1400x900')
        min_size = self.config_manager.get_value('ui', 'min_window_size', '1200x700')
        use_emojis = self.config_manager.get_value('ui', 'use_emojis', True)

        # Titre avec design moderne
        title = f"{'🏭 ' if use_emojis else ''}{app_info['name']} v{app_info['version']} - Interface Professionnelle"
        self.root.title(title)
        self.root.geometry(window_size)

        # Extraire les dimensions minimales
        min_w, min_h = map(int, min_size.split('x'))
        self.root.minsize(min_w, min_h)

        # Appliquer le thème à la fenêtre principale
        theme = self.theme_manager.get_theme()
        self.root.configure(bg=theme["colors"]["background"])

        # Centrer la fenêtre
        self.center_window()

        # Configuration des styles modernes
        self.modern_styles.configure_styles()

        # Icône de la fenêtre (si disponible)
        try:
            # Vous pouvez ajouter une icône ici
            # self.root.iconbitmap('icon.ico')
            pass
        except:
            pass

    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.root.update_idletasks()

        # Obtenir les dimensions de l'écran
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # Obtenir les dimensions de la fenêtre
        window_width = self.root.winfo_reqwidth()
        window_height = self.root.winfo_reqheight()

        # Calculer la position pour centrer
        x = (screen_width // 2) - (window_width // 2)
        y = (screen_height // 2) - (window_height // 2)

        # Appliquer la position
        self.root.geometry(f"+{x}+{y}")

    def create_menu(self):
        """Créer la barre de menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📁 Fichier", menu=file_menu)
        file_menu.add_command(label="📊 Importer Excel", command=self.import_excel)
        file_menu.add_command(label="📤 Exporter données", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="🚪 Quitter", command=self.root.quit)

        # Menu Données
        data_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📊 Données", menu=data_menu)
        data_menu.add_command(label="📦 Réceptions", command=lambda: self.show_module('reception'))
        data_menu.add_command(label="🏭 Production", command=lambda: self.show_module('production'))
        data_menu.add_command(label="⚠️ Arrêts", command=lambda: self.show_module('arrets'))
        data_menu.add_command(label="💰 Ventes", command=lambda: self.show_module('ventes'))

        # Menu Rapports
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📈 Rapports", menu=reports_menu)
        reports_menu.add_command(label="📊 Tableau de bord", command=self.show_dashboard)
        reports_menu.add_command(label="📋 Statistiques", command=self.show_statistics)
        reports_menu.add_command(label="📄 Rapport mensuel", command=self.generate_monthly_report)

        # Menu Outils
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="🔧 Outils", menu=tools_menu)
        tools_menu.add_command(label="⚙️ Configuration", command=self.show_config)
        tools_menu.add_command(label="🎨 Sélecteur de Thèmes", command=self.show_theme_selector)
        tools_menu.add_command(label="🔍 Diagnostic", command=self.run_diagnostic)
        tools_menu.add_separator()
        tools_menu.add_command(label="💾 Sauvegarde BD", command=self.backup_database)

        # Menu Aide
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="❓ Aide", menu=help_menu)
        help_menu.add_command(label="ℹ️ À propos", command=self.show_about)
        help_menu.add_command(label="📖 Documentation", command=self.show_help)

    def create_main_interface(self):
        """Créer l'interface principale moderne"""
        theme = self.theme_manager.get_theme()

        # Frame principal avec style moderne
        main_frame = ttk.Frame(self.root, style="Modern.TFrame", padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configuration du grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # En-tête moderne avec gradient simulé
        self.create_modern_header(main_frame)

        # Panel de navigation moderne (gauche)
        self.create_modern_navigation_panel(main_frame)

        # Zone de contenu principal moderne (droite)
        self.create_modern_content_area(main_frame)

        # Barre de statut moderne
        self.create_modern_status_bar()

    def create_modern_header(self, parent):
        """Créer l'en-tête moderne"""
        theme = self.theme_manager.get_theme()

        # Frame d'en-tête avec couleur de fond
        header_frame = tk.Frame(
            parent,
            bg=theme["colors"]["primary"],
            height=80
        )
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        header_frame.grid_propagate(False)

        # Titre principal
        title_label = tk.Label(
            header_frame,
            text="🏭 SOTRAMINE",
            bg=theme["colors"]["primary"],
            fg="white",
            font=theme["fonts"]["title"]
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)

        # Sous-titre
        subtitle_label = tk.Label(
            header_frame,
            text="Système de Suivi Phosphate - Interface Professionnelle",
            bg=theme["colors"]["primary"],
            fg="white",
            font=theme["fonts"]["body"]
        )
        subtitle_label.pack(side=tk.LEFT, padx=(0, 20), pady=20)

        # Informations de version (à droite)
        app_info = get_app_info()
        version_label = tk.Label(
            header_frame,
            text=f"v{app_info['version']}",
            bg=theme["colors"]["primary"],
            fg="white",
            font=theme["fonts"]["small"]
        )
        version_label.pack(side=tk.RIGHT, padx=20, pady=20)

    def create_modern_navigation_panel(self, parent):
        """Créer le panel de navigation moderne"""
        theme = self.theme_manager.get_theme()

        # Frame de navigation avec style moderne
        nav_frame = ttk.LabelFrame(
            parent,
            text="📋 Navigation",
            style="Modern.TLabelframe",
            padding="15"
        )
        nav_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))

        # Boutons de navigation avec icônes et style moderne
        nav_buttons = [
            ("📊", "Tableau de bord", self.show_dashboard, "primary"),
            ("📦", "Réceptions", lambda: self.show_module('reception'), "secondary"),
            ("🏭", "Production", lambda: self.show_module('production'), "secondary"),
            ("⚠️", "Arrêts", lambda: self.show_module('arrets'), "secondary"),
            ("💰", "Ventes", lambda: self.show_module('ventes'), "secondary"),
            ("📈", "Statistiques", self.show_statistics, "secondary"),
            ("📊", "Import Excel", self.import_excel, "accent"),
        ]

        for i, (icon, text, command, style_type) in enumerate(nav_buttons):
            # Frame pour chaque bouton
            btn_frame = tk.Frame(nav_frame, bg=theme["colors"]["background"])
            btn_frame.grid(row=i, column=0, pady=3, sticky=(tk.W, tk.E))

            # Bouton avec style moderne
            if style_type == "primary":
                bg_color = theme["colors"]["primary"]
                fg_color = "white"
            elif style_type == "accent":
                bg_color = theme["colors"]["accent"]
                fg_color = "white"
            else:
                bg_color = theme["colors"]["surface"]
                fg_color = theme["colors"]["text_primary"]

            btn = tk.Button(
                btn_frame,
                text=f"{icon} {text}",
                command=command,
                bg=bg_color,
                fg=fg_color,
                font=theme["fonts"]["body"],
                relief="flat",
                borderwidth=0,
                padx=15,
                pady=10,
                anchor="w",
                cursor="hand2"
            )
            btn.pack(fill=tk.X)

            # Effet hover (simulation)
            def on_enter(event, btn=btn, hover_color=theme["colors"]["secondary"]):
                btn.configure(bg=hover_color)

            def on_leave(event, btn=btn, original_color=bg_color):
                btn.configure(bg=original_color)

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

        nav_frame.columnconfigure(0, weight=1)

    def create_modern_content_area(self, parent):
        """Créer la zone de contenu moderne"""
        # Frame de contenu avec style moderne
        self.content_frame = ttk.LabelFrame(
            parent,
            text="📄 Contenu Principal",
            style="Modern.TLabelframe",
            padding="20"
        )
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)

        # Afficher le tableau de bord par défaut
        self.show_dashboard()

    def create_modern_status_bar(self):
        """Créer la barre de statut moderne"""
        # Utiliser la classe StatusBar du système de thèmes
        self.status_bar = StatusBar(self.root, self.theme_manager)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Maintenir la compatibilité avec l'ancien système
        self.status_var = self.status_bar.status_var

    def update_status(self, message, status_type="info"):
        """Méthode utilitaire pour mettre à jour le statut"""
        if hasattr(self, 'status_bar') and hasattr(self.status_bar, 'set_status'):
            self.status_bar.set_status(message, status_type)
        elif hasattr(self, 'status_var'):
            icons = {
                "info": "ℹ️",
                "success": "✅",
                "warning": "⚠️",
                "error": "❌",
                "loading": "🔄"
            }
            icon = icons.get(status_type, "ℹ️")
            self.status_var.set(f"{icon} {message}")

    def create_navigation_panel(self, parent):
        """Créer le panel de navigation"""
        nav_frame = ttk.LabelFrame(parent, text="📋 Navigation", padding="10")
        nav_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        # Boutons de navigation
        nav_buttons = [
            ("📊 Tableau de bord", self.show_dashboard),
            ("📦 Réceptions", lambda: self.show_module('reception')),
            ("🏭 Production", lambda: self.show_module('production')),
            ("⚠️ Arrêts", lambda: self.show_module('arrets')),
            ("💰 Ventes", lambda: self.show_module('ventes')),
            ("📈 Statistiques", self.show_statistics),
            ("📊 Import Excel", self.import_excel),
        ]

        for i, (text, command) in enumerate(nav_buttons):
            btn = ttk.Button(nav_frame, text=text, command=command, width=20)
            btn.grid(row=i, column=0, pady=2, sticky=(tk.W, tk.E))

        nav_frame.columnconfigure(0, weight=1)

    def create_content_area(self, parent):
        """Créer la zone de contenu principal"""
        self.content_frame = ttk.LabelFrame(parent, text="📄 Contenu", padding="10")
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)

        # Afficher le tableau de bord par défaut
        self.show_dashboard()

    def create_status_bar(self):
        """Créer la barre de statut"""
        self.status_var.set("✅ Prêt")

        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))

    def clear_content(self):
        """Vider la zone de contenu"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        """Afficher le tableau de bord moderne"""
        self.clear_content()
        self.update_status("Tableau de bord chargé", "success")

        # Titre moderne
        title = ttk.Label(self.content_frame, text="📊 Tableau de Bord Exécutif", style='Title.TLabel')
        title.grid(row=0, column=0, columnspan=3, pady=(0, 30))

        # Obtenir les statistiques
        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            productions = ProductionManager.obtenir_production()
            ventes = VenteManager.obtenir_ventes()

            # Frame pour les cartes de statistiques
            cards_frame = ttk.Frame(self.content_frame, style="Modern.TFrame")
            cards_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 30))

            # Cartes de statistiques modernes
            self.create_modern_stat_cards(cards_frame, receptions, productions, ventes)

            # Section des graphiques et activités
            self.create_dashboard_sections(receptions, productions, ventes)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement des données: {e}")

    def create_modern_stat_cards(self, parent, receptions, productions, ventes):
        """Créer les cartes de statistiques modernes"""
        theme = self.theme_manager.get_theme()

        # Calculer les statistiques
        stats = [
            {
                "title": "Réceptions",
                "value": len(receptions),
                "subtitle": "Total enregistrements",
                "icon": "📦",
                "color": theme["colors"]["primary"]
            },
            {
                "title": "Productions",
                "value": len(productions),
                "subtitle": "Cycles de production",
                "icon": "🏭",
                "color": theme["colors"]["secondary"]
            },
            {
                "title": "Ventes",
                "value": len(ventes),
                "subtitle": "Commandes clients",
                "icon": "💰",
                "color": theme["colors"]["success"]
            },
            {
                "title": "Tonnage Total",
                "value": f"{sum(r.get('tonnage', 0) for r in receptions):,.0f}",
                "subtitle": "Tonnes reçues",
                "icon": "⚖️",
                "color": theme["colors"]["accent"]
            }
        ]

        for i, stat in enumerate(stats):
            self.create_professional_card(parent, stat, i)

    def create_professional_card(self, parent, stat_data, position):
        """Créer une carte professionnelle"""
        theme = self.theme_manager.get_theme()

        # Frame principale de la carte
        card_frame = tk.Frame(
            parent,
            bg="white",
            relief="solid",
            borderwidth=1,
            padx=20,
            pady=15
        )
        card_frame.grid(row=position//2, column=position%2, padx=10, pady=10, sticky=(tk.W, tk.E))

        # En-tête avec icône et couleur
        header_frame = tk.Frame(card_frame, bg=stat_data["color"], height=5)
        header_frame.pack(fill=tk.X, pady=(0, 15))

        # Icône et titre
        title_frame = tk.Frame(card_frame, bg="white")
        title_frame.pack(fill=tk.X)

        icon_label = tk.Label(
            title_frame,
            text=stat_data["icon"],
            bg="white",
            font=("Segoe UI", 24),
            fg=stat_data["color"]
        )
        icon_label.pack(side=tk.LEFT)

        title_label = tk.Label(
            title_frame,
            text=stat_data["title"],
            bg="white",
            fg=theme["colors"]["text_primary"],
            font=theme["fonts"]["subheading"]
        )
        title_label.pack(side=tk.LEFT, padx=(10, 0))

        # Valeur principale
        value_label = tk.Label(
            card_frame,
            text=str(stat_data["value"]),
            bg="white",
            fg=stat_data["color"],
            font=("Segoe UI", 28, "bold")
        )
        value_label.pack(pady=(10, 5))

        # Sous-titre
        subtitle_label = tk.Label(
            card_frame,
            text=stat_data["subtitle"],
            bg="white",
            fg=theme["colors"]["text_secondary"],
            font=theme["fonts"]["small"]
        )
        subtitle_label.pack()

        # Configurer la grille
        parent.columnconfigure(position%2, weight=1)

    def create_dashboard_sections(self, receptions, productions, ventes):
        """Créer les sections du tableau de bord"""
        # Section activité récente
        activity_frame = ttk.LabelFrame(
            self.content_frame,
            text="📈 Activité Récente",
            style="Modern.TLabelframe",
            padding="15"
        )
        activity_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15), padx=(0, 10))

        # Section indicateurs
        indicators_frame = ttk.LabelFrame(
            self.content_frame,
            text="🎯 Indicateurs Clés",
            style="Modern.TLabelframe",
            padding="15"
        )
        indicators_frame.grid(row=2, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 15))

        # Remplir les sections
        self.create_modern_activity_list(activity_frame, receptions)
        self.create_key_indicators(indicators_frame, receptions, productions, ventes)

        # Configuration du grid
        self.content_frame.columnconfigure(0, weight=2)
        self.content_frame.columnconfigure(1, weight=2)
        self.content_frame.columnconfigure(2, weight=1)
        self.content_frame.rowconfigure(2, weight=1)

    def create_modern_activity_list(self, parent, receptions):
        """Créer la liste d'activité moderne"""
        # Treeview avec style moderne
        columns = ('Type', 'Date', 'Description', 'Valeur')
        tree = ttk.Treeview(parent, columns=columns, show='headings', height=8, style="Modern.Treeview")

        # Configuration des colonnes
        widths = [100, 100, 200, 100]
        for i, col in enumerate(columns):
            tree.heading(col, text=col)
            tree.column(col, width=widths[i])

        # Scrollbar moderne
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # Placement
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)

        # Charger les données récentes avec couleurs
        try:
            for i, reception in enumerate(receptions[:8]):
                tags = ['even' if i % 2 == 0 else 'odd']
                tree.insert('', tk.END, values=(
                    '📦 Réception',
                    reception['date_reception'],
                    f"Voyage {reception['numero_voyage']}",
                    f"{reception['tonnage']:.0f} T"
                ), tags=tags)

            # Configuration des couleurs alternées
            theme = self.theme_manager.get_theme()
            tree.tag_configure('even', background=theme["colors"]["surface"])
            tree.tag_configure('odd', background=theme["colors"]["background"])

        except Exception as e:
            tree.insert('', tk.END, values=('❌ Erreur', '', str(e), ''))

    def create_key_indicators(self, parent, receptions, productions, ventes):
        """Créer les indicateurs clés"""
        theme = self.theme_manager.get_theme()

        # Calculer les indicateurs
        indicators = []

        # Qualité moyenne
        if receptions:
            p2o5_values = [r.get('p2o5_pourcentage') for r in receptions if r.get('p2o5_pourcentage')]
            if p2o5_values:
                avg_quality = sum(p2o5_values) / len(p2o5_values)
                indicators.append(("Qualité P2O5", f"{avg_quality:.1f}%", "🎯"))

        # Dernière activité
        if receptions:
            last_date = receptions[0]['date_reception']
            indicators.append(("Dernière réception", last_date, "📅"))

        # État système
        indicators.append(("Système", "✅ Opérationnel", "💾"))
        indicators.append(("Modules", "6 actifs", "🔧"))

        # Afficher les indicateurs
        for i, (label, value, icon) in enumerate(indicators):
            indicator_frame = tk.Frame(parent, bg=theme["colors"]["background"])
            indicator_frame.pack(fill=tk.X, pady=5)

            icon_label = tk.Label(
                indicator_frame,
                text=icon,
                bg=theme["colors"]["background"],
                font=("Segoe UI", 16)
            )
            icon_label.pack(side=tk.LEFT, padx=(0, 10))

            text_frame = tk.Frame(indicator_frame, bg=theme["colors"]["background"])
            text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

            label_widget = tk.Label(
                text_frame,
                text=label,
                bg=theme["colors"]["background"],
                fg=theme["colors"]["text_secondary"],
                font=theme["fonts"]["small"],
                anchor="w"
            )
            label_widget.pack(anchor="w")

            value_widget = tk.Label(
                text_frame,
                text=value,
                bg=theme["colors"]["background"],
                fg=theme["colors"]["text_primary"],
                font=theme["fonts"]["body"],
                anchor="w"
            )
            value_widget.pack(anchor="w")

    def create_stat_card(self, parent, title, value, row, col):
        """Créer une carte de statistique"""
        card_frame = ttk.LabelFrame(parent, text=title, padding="10")
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky=(tk.W, tk.E))

        value_label = ttk.Label(card_frame, text=str(value), font=('Arial', 14, 'bold'))
        value_label.grid(row=0, column=0)

        parent.columnconfigure(col, weight=1)

    def create_recent_activity(self, parent):
        """Créer la liste d'activité récente"""
        # Treeview pour afficher les données
        columns = ('Type', 'Date', 'Description', 'Valeur')
        tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)

        # Configuration des colonnes
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)

        # Scrollbar
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        # Placement
        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        parent.columnconfigure(0, weight=1)
        parent.rowconfigure(0, weight=1)

        # Charger les données récentes
        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()[:5]
            for reception in receptions:
                tree.insert('', tk.END, values=(
                    '📦 Réception',
                    reception['date_reception'],
                    f"Voyage {reception['numero_voyage']}",
                    f"{reception['tonnage']} T"
                ))
        except Exception as e:
            tree.insert('', tk.END, values=('❌ Erreur', '', str(e), ''))

    def show_module(self, module_name):
        """Afficher un module spécifique"""
        self.clear_content()

        if module_name == 'reception':
            self.show_reception_module()
        elif module_name == 'production':
            self.show_production_module()
        elif module_name == 'arrets':
            self.show_arrets_module()
        elif module_name == 'ventes':
            self.show_ventes_module()

    def show_reception_module(self):
        """Afficher le module de réception"""
        self.status_var.set("📦 Module Réceptions")

        # Titre
        title = ttk.Label(self.content_frame, text="📦 Gestion des Réceptions", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Boutons d'action
        btn_frame = ttk.Frame(self.content_frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="➕ Nouvelle Réception", command=self.add_reception).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📊 Statistiques", command=self.show_reception_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🔄 Actualiser", command=self.refresh_receptions).pack(side=tk.LEFT)

        # Table des réceptions
        self.create_reception_table()

    def create_reception_table(self):
        """Créer la table des réceptions"""
        # Frame pour la table
        table_frame = ttk.Frame(self.content_frame)
        table_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Colonnes
        columns = ('ID', 'Voyage', 'Date', 'Tonnage', 'P2O5%', 'Observations')
        self.reception_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        widths = [50, 80, 100, 100, 80, 200]
        for i, col in enumerate(columns):
            self.reception_tree.heading(col, text=col)
            self.reception_tree.column(col, width=widths[i])

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.reception_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.reception_tree.xview)
        self.reception_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Placement
        self.reception_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        self.content_frame.rowconfigure(2, weight=1)

        # Événement double-clic pour modifier
        self.reception_tree.bind('<Double-1>', self.on_reception_double_click)

        # Charger les données
        self.refresh_receptions()

    def refresh_receptions(self):
        """Actualiser la liste des réceptions"""
        # Vider la table
        for item in self.reception_tree.get_children():
            self.reception_tree.delete(item)

        try:
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            for reception in receptions[:50]:  # Limiter à 50 pour la performance
                self.reception_tree.insert('', tk.END, values=(
                    reception['id'],
                    reception['numero_voyage'],
                    reception['date_reception'],
                    f"{reception['tonnage']:.1f}" if reception['tonnage'] else '',
                    f"{reception['p2o5_pourcentage']:.1f}" if reception['p2o5_pourcentage'] else '',
                    reception['observations'] or ''
                ))

            self.status_var.set(f"📦 {len(receptions)} réceptions chargées")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")

    def load_initial_data(self):
        """Charger les données initiales"""
        try:
            # Vérifier la connexion à la base de données
            tables = db_manager.get_all_tables()
            self.status_var.set(f"✅ Base de données connectée ({len(tables)} tables)")
        except Exception as e:
            messagebox.showerror("Erreur de base de données", f"Impossible de se connecter à la base de données: {e}")

    def add_reception(self):
        """Ajouter une nouvelle réception"""
        result = show_reception_dialog(self.root)
        if result:
            messagebox.showinfo("Succès", f"Réception ajoutée avec l'ID: {result}")
            self.refresh_receptions()

    def on_reception_double_click(self, event):
        """Gestionnaire de double-clic sur une réception"""
        selection = self.reception_tree.selection()
        if selection:
            item = self.reception_tree.item(selection[0])
            reception_id = item['values'][0]

            # Récupérer les données complètes de la réception
            try:
                receptions = ReceptionPhosphateManager.obtenir_receptions()
                reception_data = next((r for r in receptions if r['id'] == reception_id), None)

                if reception_data:
                    result = show_reception_dialog(self.root, reception_data)
                    if result:
                        messagebox.showinfo("Succès", "Réception modifiée avec succès")
                        self.refresh_receptions()
                else:
                    messagebox.showerror("Erreur", "Réception non trouvée")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la modification: {e}")

    def show_reception_stats(self):
        """Afficher les statistiques des réceptions"""
        self.clear_content()
        self.status_var.set("📈 Statistiques Réceptions")

        # Titre
        title = ttk.Label(self.content_frame, text="📈 Statistiques des Réceptions", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        try:
            # Obtenir les statistiques générales
            receptions = ReceptionPhosphateManager.obtenir_receptions()

            if not receptions:
                ttk.Label(self.content_frame, text="Aucune donnée disponible").grid(row=1, column=0)
                return

            # Frame pour les statistiques
            stats_frame = ttk.LabelFrame(self.content_frame, text="📊 Statistiques Générales", padding="10")
            stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

            # Calculer les statistiques
            total_receptions = len(receptions)
            tonnage_total = sum(r.get('tonnage', 0) for r in receptions if r.get('tonnage'))
            tonnage_moyen = tonnage_total / total_receptions if total_receptions > 0 else 0

            # Analyses chimiques moyennes
            p2o5_values = [r.get('p2o5_pourcentage') for r in receptions if r.get('p2o5_pourcentage')]
            p2o5_moyen = sum(p2o5_values) / len(p2o5_values) if p2o5_values else 0

            # Afficher les statistiques
            stats_data = [
                ("Nombre total de réceptions", f"{total_receptions:,}"),
                ("Tonnage total", f"{tonnage_total:,.1f} T"),
                ("Tonnage moyen par réception", f"{tonnage_moyen:,.1f} T"),
                ("P2O5 moyen", f"{p2o5_moyen:.2f}%" if p2o5_moyen > 0 else "N/A"),
            ]

            for i, (label, value) in enumerate(stats_data):
                ttk.Label(stats_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
                ttk.Label(stats_frame, text=value, font=('Arial', 10, 'bold')).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)

            # Graphique par mois (simulation)
            chart_frame = ttk.LabelFrame(self.content_frame, text="📈 Répartition Mensuelle", padding="10")
            chart_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

            # Créer un tableau simple pour les données mensuelles
            monthly_tree = ttk.Treeview(chart_frame, columns=('Mois', 'Réceptions', 'Tonnage'), show='headings', height=8)
            monthly_tree.heading('Mois', text='Mois')
            monthly_tree.heading('Réceptions', text='Réceptions')
            monthly_tree.heading('Tonnage', text='Tonnage (T)')

            # Calculer les données mensuelles
            monthly_data = {}
            for reception in receptions:
                date_str = reception.get('date_reception', '')
                if date_str:
                    try:
                        date_obj = datetime.strptime(str(date_str), '%Y-%m-%d')
                        month_key = date_obj.strftime('%Y-%m')

                        if month_key not in monthly_data:
                            monthly_data[month_key] = {'count': 0, 'tonnage': 0}

                        monthly_data[month_key]['count'] += 1
                        monthly_data[month_key]['tonnage'] += reception.get('tonnage', 0)
                    except:
                        continue

            # Afficher les données mensuelles
            for month, data in sorted(monthly_data.items()):
                monthly_tree.insert('', tk.END, values=(
                    month,
                    data['count'],
                    f"{data['tonnage']:,.1f}"
                ))

            monthly_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
            chart_frame.columnconfigure(0, weight=1)
            chart_frame.rowconfigure(0, weight=1)
            self.content_frame.rowconfigure(2, weight=1)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du calcul des statistiques: {e}")

    def show_production_module(self):
        """Afficher le module de production"""
        self.clear_content()
        self.status_var.set("🏭 Module Production")

        # Titre
        title = ttk.Label(self.content_frame, text="🏭 Gestion de la Production", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Boutons d'action
        btn_frame = ttk.Frame(self.content_frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="➕ Nouvelle Production", command=self.add_production).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📊 Rendements", command=self.show_production_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🔄 Actualiser", command=self.refresh_production).pack(side=tk.LEFT)

        # Table de production
        self.create_production_table()

    def create_production_table(self):
        """Créer la table de production"""
        # Frame pour la table
        table_frame = ttk.Frame(self.content_frame)
        table_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Colonnes
        columns = ('ID', 'Date', 'Régime', 'Réception (T)', 'Production (T)', 'Rendement (%)', 'Heures Marche')
        self.production_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        widths = [50, 100, 80, 120, 120, 100, 120]
        for i, col in enumerate(columns):
            self.production_tree.heading(col, text=col)
            self.production_tree.column(col, width=widths[i])

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.production_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.production_tree.xview)
        self.production_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Placement
        self.production_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        self.content_frame.rowconfigure(2, weight=1)

        # Événement double-clic pour modifier
        self.production_tree.bind('<Double-1>', self.on_production_double_click)

        # Charger les données
        self.refresh_production()

    def refresh_production(self):
        """Actualiser la liste de production"""
        # Vider la table
        for item in self.production_tree.get_children():
            self.production_tree.delete(item)

        try:
            productions = ProductionManager.obtenir_production()
            for prod in productions[:50]:  # Limiter à 50 pour la performance
                # Calculer le rendement
                rendement = ""
                if prod.get('total_reception_phosphate_brut') and prod.get('total_production_concentre'):
                    rendement_val = (prod['total_production_concentre'] / prod['total_reception_phosphate_brut']) * 100
                    rendement = f"{rendement_val:.1f}"

                self.production_tree.insert('', tk.END, values=(
                    prod['id'],
                    prod['date_production'],
                    f"{prod['regime_travail']}h" if prod['regime_travail'] else '',
                    f"{prod['total_reception_phosphate_brut']:.1f}" if prod['total_reception_phosphate_brut'] else '',
                    f"{prod['total_production_concentre']:.1f}" if prod['total_production_concentre'] else '',
                    rendement,
                    f"{prod['total_heures_marche']:.1f}" if prod['total_heures_marche'] else ''
                ))

            self.status_var.set(f"🏭 {len(productions)} productions chargées")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")

    def add_production(self):
        """Ajouter une nouvelle production"""
        result = show_production_dialog(self.root)
        if result:
            messagebox.showinfo("Succès", f"Production ajoutée avec l'ID: {result}")
            self.refresh_production()

    def on_production_double_click(self, event):
        """Gestionnaire de double-clic sur une production"""
        selection = self.production_tree.selection()
        if selection:
            item = self.production_tree.item(selection[0])
            production_id = item['values'][0]
            messagebox.showinfo("Modification", f"Modification de la production ID: {production_id}\n(Fonctionnalité en développement)")

    def show_production_stats(self):
        """Afficher les statistiques de production"""
        messagebox.showinfo("Statistiques", "Statistiques de production en cours de développement")

    def show_arrets_module(self):
        """Afficher le module des arrêts"""
        self.clear_content()
        self.status_var.set("⚠️ Module Arrêts")

        # Titre
        title = ttk.Label(self.content_frame, text="⚠️ Gestion des Arrêts", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Sélection du type d'arrêt
        type_frame = ttk.Frame(self.content_frame)
        type_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(type_frame, text="Type d'arrêt:").pack(side=tk.LEFT, padx=(0, 10))

        self.arret_type_var = tk.StringVar(value="laverie")
        type_combo = ttk.Combobox(type_frame, textvariable=self.arret_type_var,
                                 values=["laverie", "concentrateur"], state="readonly", width=15)
        type_combo.pack(side=tk.LEFT, padx=(0, 20))
        type_combo.bind('<<ComboboxSelected>>', self.on_arret_type_change)

        # Boutons d'action
        btn_frame = ttk.Frame(self.content_frame)
        btn_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="➕ Nouvel Arrêt", command=self.add_arret).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📊 Statistiques", command=self.show_arret_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🔄 Actualiser", command=self.refresh_arrets).pack(side=tk.LEFT)

        # Table des arrêts
        self.create_arrets_table()

    def create_arrets_table(self):
        """Créer la table des arrêts"""
        # Frame pour la table
        table_frame = ttk.Frame(self.content_frame)
        table_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Colonnes
        columns = ('ID', 'Date', 'Type', 'Heures Marche', 'Heures Arrêt', 'Cause 1', 'Durée 1', 'Disponibilité %')
        self.arrets_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        widths = [50, 100, 100, 100, 100, 150, 80, 100]
        for i, col in enumerate(columns):
            self.arrets_tree.heading(col, text=col)
            self.arrets_tree.column(col, width=widths[i])

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.arrets_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.arrets_tree.xview)
        self.arrets_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Placement
        self.arrets_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        self.content_frame.rowconfigure(3, weight=1)

        # Événement double-clic pour modifier
        self.arrets_tree.bind('<Double-1>', self.on_arret_double_click)

        # Charger les données
        self.refresh_arrets()

    def on_arret_type_change(self, event=None):
        """Gestionnaire de changement de type d'arrêt"""
        self.refresh_arrets()

    def refresh_arrets(self):
        """Actualiser la liste des arrêts"""
        # Vider la table
        for item in self.arrets_tree.get_children():
            self.arrets_tree.delete(item)

        try:
            arret_type = self.arret_type_var.get()
            table_name = f"arrets_{arret_type}"

            arrets = ArretManager.obtenir_arrets(table_name)

            for arret in arrets[:50]:  # Limiter à 50 pour la performance
                # Calculer la disponibilité
                disponibilite = ""
                if arret.get('total_heures_marche') and arret.get('total_heures_arret'):
                    total_heures = arret['total_heures_marche'] + arret['total_heures_arret']
                    if total_heures > 0:
                        disp_val = (arret['total_heures_marche'] / total_heures) * 100
                        disponibilite = f"{disp_val:.1f}"

                self.arrets_tree.insert('', tk.END, values=(
                    arret['id'],
                    arret['date_arret'],
                    arret_type.capitalize(),
                    f"{arret['total_heures_marche']:.1f}" if arret['total_heures_marche'] else '',
                    f"{arret['total_heures_arret']:.1f}" if arret['total_heures_arret'] else '',
                    arret['cause_arret_1'] or '',
                    f"{arret['duree_arret_1']:.1f}" if arret['duree_arret_1'] else '',
                    disponibilite
                ))

            self.status_var.set(f"⚠️ {len(arrets)} arrêts {arret_type} chargés")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")

    def add_arret(self):
        """Ajouter un nouvel arrêt"""
        from gui_dialogs import show_arret_dialog
        arret_type = self.arret_type_var.get()
        result = show_arret_dialog(self.root, arret_type)
        if result:
            messagebox.showinfo("Succès", f"Arrêt {arret_type} ajouté avec l'ID: {result}")
            self.refresh_arrets()

    def on_arret_double_click(self, event):
        """Gestionnaire de double-clic sur un arrêt"""
        selection = self.arrets_tree.selection()
        if selection:
            item = self.arrets_tree.item(selection[0])
            arret_id = item['values'][0]
            messagebox.showinfo("Modification", f"Modification de l'arrêt ID: {arret_id}\n(Fonctionnalité en développement)")

    def show_arret_stats(self):
        """Afficher les statistiques des arrêts"""
        self.clear_content()
        self.status_var.set("📈 Statistiques Arrêts")

        # Titre
        title = ttk.Label(self.content_frame, text="📈 Statistiques des Arrêts", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        try:
            # Statistiques pour laverie et concentrateur
            for i, arret_type in enumerate(['laverie', 'concentrateur']):
                stats_frame = ttk.LabelFrame(self.content_frame, text=f"📊 {arret_type.capitalize()}", padding="10")
                stats_frame.grid(row=1+i, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

                table_name = f"arrets_{arret_type}"
                arrets = ArretManager.obtenir_arrets(table_name)

                if arrets:
                    # Calculer les statistiques
                    total_arrets = len(arrets)
                    total_heures_arret = sum(a.get('total_heures_arret', 0) for a in arrets)
                    total_heures_marche = sum(a.get('total_heures_marche', 0) for a in arrets)

                    # Disponibilité moyenne
                    if total_heures_marche + total_heures_arret > 0:
                        disponibilite_moy = (total_heures_marche / (total_heures_marche + total_heures_arret)) * 100
                    else:
                        disponibilite_moy = 0

                    # Causes principales
                    causes = {}
                    for arret in arrets:
                        if arret.get('cause_arret_1'):
                            cause = arret['cause_arret_1']
                            causes[cause] = causes.get(cause, 0) + 1

                    cause_principale = max(causes.items(), key=lambda x: x[1])[0] if causes else "N/A"

                    # Afficher les statistiques
                    stats_data = [
                        ("Nombre total d'arrêts", f"{total_arrets:,}"),
                        ("Total heures d'arrêt", f"{total_heures_arret:.1f} h"),
                        ("Total heures de marche", f"{total_heures_marche:.1f} h"),
                        ("Disponibilité moyenne", f"{disponibilite_moy:.1f}%"),
                        ("Cause principale", cause_principale),
                    ]

                    for j, (label, value) in enumerate(stats_data):
                        ttk.Label(stats_frame, text=f"{label}:").grid(row=j, column=0, sticky=tk.W, pady=2)
                        ttk.Label(stats_frame, text=value, font=('Arial', 10, 'bold')).grid(row=j, column=1, sticky=tk.W, padx=(20, 0), pady=2)
                else:
                    ttk.Label(stats_frame, text="Aucune donnée disponible").grid(row=0, column=0)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du calcul des statistiques: {e}")

    def show_ventes_module(self):
        """Afficher le module des ventes"""
        self.clear_content()
        self.status_var.set("💰 Module Ventes")

        # Titre
        title = ttk.Label(self.content_frame, text="💰 Gestion des Ventes", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Boutons d'action
        btn_frame = ttk.Frame(self.content_frame)
        btn_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(btn_frame, text="➕ Nouvelle Vente", command=self.add_vente).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📊 Statistiques", command=self.show_vente_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🔄 Actualiser", command=self.refresh_ventes).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="📋 Planning", command=self.show_planning_ventes).pack(side=tk.LEFT)

        # Table des ventes
        self.create_ventes_table()

    def create_ventes_table(self):
        """Créer la table des ventes"""
        # Frame pour la table
        table_frame = ttk.Frame(self.content_frame)
        table_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        # Colonnes
        columns = ('ID', 'Date Vente', 'Client', 'Quantité (T)', 'Qualité P2O5 (%)', 'Date Cargaison', 'Statut')
        self.ventes_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configuration des colonnes
        widths = [50, 100, 150, 100, 120, 120, 100]
        for i, col in enumerate(columns):
            self.ventes_tree.heading(col, text=col)
            self.ventes_tree.column(col, width=widths[i])

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.ventes_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.ventes_tree.xview)
        self.ventes_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Placement
        self.ventes_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)
        self.content_frame.rowconfigure(2, weight=1)

        # Événement double-clic pour modifier
        self.ventes_tree.bind('<Double-1>', self.on_vente_double_click)

        # Menu contextuel
        self.create_vente_context_menu()

        # Charger les données
        self.refresh_ventes()

    def create_vente_context_menu(self):
        """Créer le menu contextuel pour les ventes"""
        self.vente_context_menu = tk.Menu(self.root, tearoff=0)
        self.vente_context_menu.add_command(label="✏️ Modifier", command=self.edit_selected_vente)
        self.vente_context_menu.add_command(label="📋 Détails", command=self.show_vente_details)
        self.vente_context_menu.add_separator()
        self.vente_context_menu.add_command(label="✅ Marquer Confirmée", command=lambda: self.change_vente_status("Confirmée"))
        self.vente_context_menu.add_command(label="🚛 Marquer En cours", command=lambda: self.change_vente_status("En cours"))
        self.vente_context_menu.add_command(label="📦 Marquer Livrée", command=lambda: self.change_vente_status("Livrée"))
        self.vente_context_menu.add_command(label="❌ Marquer Annulée", command=lambda: self.change_vente_status("Annulée"))

        # Lier le menu contextuel
        self.ventes_tree.bind("<Button-3>", self.show_vente_context_menu)

    def show_vente_context_menu(self, event):
        """Afficher le menu contextuel"""
        # Sélectionner l'item sous le curseur
        item = self.ventes_tree.identify_row(event.y)
        if item:
            self.ventes_tree.selection_set(item)
            self.vente_context_menu.post(event.x_root, event.y_root)

    def refresh_ventes(self):
        """Actualiser la liste des ventes"""
        # Vider la table
        for item in self.ventes_tree.get_children():
            self.ventes_tree.delete(item)

        try:
            ventes = VenteManager.obtenir_ventes()

            for vente in ventes[:50]:  # Limiter à 50 pour la performance
                # Colorer selon le statut
                tags = []
                statut = vente.get('statut_vente', 'Planifiée')
                if statut == 'Livrée':
                    tags = ['delivered']
                elif statut == 'Annulée':
                    tags = ['cancelled']
                elif statut == 'En cours':
                    tags = ['in_progress']

                self.ventes_tree.insert('', tk.END, values=(
                    vente['id'],
                    vente['date_vente'],
                    vente['client'],
                    f"{vente['quantite_demandee']:.1f}" if vente['quantite_demandee'] else '',
                    f"{vente['qualite_p2o5_pourcentage']:.1f}" if vente['qualite_p2o5_pourcentage'] else '',
                    vente['date_cargaison'] or '',
                    statut
                ), tags=tags)

            # Configuration des couleurs
            self.ventes_tree.tag_configure('delivered', background='#d4edda')
            self.ventes_tree.tag_configure('cancelled', background='#f8d7da')
            self.ventes_tree.tag_configure('in_progress', background='#fff3cd')

            self.status_var.set(f"💰 {len(ventes)} ventes chargées")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement: {e}")

    def add_vente(self):
        """Ajouter une nouvelle vente"""
        from gui_dialogs import show_vente_dialog
        result = show_vente_dialog(self.root)
        if result:
            messagebox.showinfo("Succès", f"Vente ajoutée avec l'ID: {result}")
            self.refresh_ventes()

    def on_vente_double_click(self, event):
        """Gestionnaire de double-clic sur une vente"""
        self.edit_selected_vente()

    def edit_selected_vente(self):
        """Modifier la vente sélectionnée"""
        selection = self.ventes_tree.selection()
        if selection:
            item = self.ventes_tree.item(selection[0])
            vente_id = item['values'][0]
            messagebox.showinfo("Modification", f"Modification de la vente ID: {vente_id}\n(Fonctionnalité en développement)")

    def show_vente_details(self):
        """Afficher les détails de la vente"""
        selection = self.ventes_tree.selection()
        if selection:
            item = self.ventes_tree.item(selection[0])
            values = item['values']

            details = f"""
Détails de la vente:

ID: {values[0]}
Date de vente: {values[1]}
Client: {values[2]}
Quantité: {values[3]} tonnes
Qualité P2O5: {values[4]}%
Date de cargaison: {values[5]}
Statut: {values[6]}
            """
            messagebox.showinfo("Détails de la vente", details)

    def change_vente_status(self, nouveau_statut):
        """Changer le statut d'une vente"""
        selection = self.ventes_tree.selection()
        if selection:
            item = self.ventes_tree.item(selection[0])
            vente_id = item['values'][0]

            try:
                VenteManager.mettre_a_jour_statut_vente(vente_id, nouveau_statut)
                messagebox.showinfo("Succès", f"Statut mis à jour: {nouveau_statut}")
                self.refresh_ventes()
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la mise à jour: {e}")

    def show_planning_ventes(self):
        """Afficher le planning des ventes"""
        self.clear_content()
        self.status_var.set("📋 Planning des Ventes")

        # Titre
        title = ttk.Label(self.content_frame, text="📋 Planning des Ventes", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Bouton retour
        ttk.Button(self.content_frame, text="🔙 Retour aux ventes",
                  command=self.show_ventes_module).grid(row=1, column=0, sticky=tk.W, pady=(0, 10))

        try:
            # Planning par statut
            ventes = VenteManager.obtenir_ventes()

            # Grouper par statut
            ventes_par_statut = {}
            for vente in ventes:
                statut = vente.get('statut_vente', 'Planifiée')
                if statut not in ventes_par_statut:
                    ventes_par_statut[statut] = []
                ventes_par_statut[statut].append(vente)

            # Afficher chaque groupe
            row = 2
            for statut, ventes_statut in ventes_par_statut.items():
                # Frame pour ce statut
                statut_frame = ttk.LabelFrame(self.content_frame, text=f"{statut} ({len(ventes_statut)})", padding="10")
                statut_frame.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

                # Liste des ventes pour ce statut
                for i, vente in enumerate(ventes_statut[:5]):  # Limiter à 5 par statut
                    vente_text = f"• {vente['client']} - {vente['quantite_demandee']:.0f}T - {vente['date_vente']}"
                    ttk.Label(statut_frame, text=vente_text).grid(row=i, column=0, sticky=tk.W, pady=1)

                if len(ventes_statut) > 5:
                    ttk.Label(statut_frame, text=f"... et {len(ventes_statut) - 5} autres",
                             font=('Arial', 8, 'italic')).grid(row=5, column=0, sticky=tk.W, pady=1)

                row += 1

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du chargement du planning: {e}")

    def show_vente_stats(self):
        """Afficher les statistiques des ventes"""
        self.clear_content()
        self.status_var.set("📈 Statistiques Ventes")

        # Titre
        title = ttk.Label(self.content_frame, text="📈 Statistiques des Ventes", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        try:
            ventes = VenteManager.obtenir_ventes()

            if not ventes:
                ttk.Label(self.content_frame, text="Aucune donnée de vente disponible").grid(row=1, column=0)
                return

            # Statistiques générales
            stats_frame = ttk.LabelFrame(self.content_frame, text="📊 Statistiques Générales", padding="10")
            stats_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

            # Calculer les statistiques
            total_ventes = len(ventes)
            quantite_totale = sum(v.get('quantite_demandee', 0) for v in ventes)
            quantite_moyenne = quantite_totale / total_ventes if total_ventes > 0 else 0

            # Qualité moyenne
            qualites = [v.get('qualite_p2o5_pourcentage') for v in ventes if v.get('qualite_p2o5_pourcentage')]
            qualite_moyenne = sum(qualites) / len(qualites) if qualites else 0

            # Clients uniques
            clients = set(v['client'] for v in ventes if v.get('client'))

            # Répartition par statut
            statuts = {}
            for vente in ventes:
                statut = vente.get('statut_vente', 'Planifiée')
                statuts[statut] = statuts.get(statut, 0) + 1

            # Afficher les statistiques générales
            stats_data = [
                ("Nombre total de ventes", f"{total_ventes:,}"),
                ("Quantité totale", f"{quantite_totale:,.0f} T"),
                ("Quantité moyenne", f"{quantite_moyenne:,.0f} T"),
                ("Qualité P2O5 moyenne", f"{qualite_moyenne:.2f}%" if qualite_moyenne > 0 else "N/A"),
                ("Nombre de clients", f"{len(clients)}"),
            ]

            for i, (label, value) in enumerate(stats_data):
                ttk.Label(stats_frame, text=f"{label}:").grid(row=i, column=0, sticky=tk.W, pady=2)
                ttk.Label(stats_frame, text=value, font=('Arial', 10, 'bold')).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)

            # Répartition par statut
            statut_frame = ttk.LabelFrame(self.content_frame, text="📋 Répartition par Statut", padding="10")
            statut_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

            for i, (statut, count) in enumerate(statuts.items()):
                pourcentage = (count / total_ventes) * 100
                ttk.Label(statut_frame, text=f"{statut}:").grid(row=i, column=0, sticky=tk.W, pady=2)
                ttk.Label(statut_frame, text=f"{count} ({pourcentage:.1f}%)",
                         font=('Arial', 10, 'bold')).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du calcul des statistiques: {e}")

    def show_statistics(self):
        """Afficher les statistiques globales"""
        self.clear_content()
        self.status_var.set("📈 Statistiques Globales")

        # Titre
        title = ttk.Label(self.content_frame, text="📈 Statistiques Globales SOTRAMINE", style='Heading.TLabel')
        title.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # Boutons de navigation
        nav_frame = ttk.Frame(self.content_frame)
        nav_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(nav_frame, text="📊 Vue d'ensemble", command=self.show_overview_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(nav_frame, text="📈 Tendances", command=self.show_trends_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(nav_frame, text="🎯 KPI", command=self.show_kpi_stats).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(nav_frame, text="📋 Rapport", command=self.generate_global_report).pack(side=tk.LEFT)

        # Afficher la vue d'ensemble par défaut
        self.show_overview_stats()

    def show_overview_stats(self):
        """Afficher la vue d'ensemble des statistiques"""
        # Nettoyer le contenu existant (sauf titre et navigation)
        for widget in self.content_frame.winfo_children()[2:]:
            widget.destroy()

        try:
            # Obtenir toutes les données
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            productions = ProductionManager.obtenir_production()
            arrets_laverie = ArretManager.obtenir_arrets('arrets_laverie')
            arrets_concentrateur = ArretManager.obtenir_arrets('arrets_concentrateur')
            ventes = VenteManager.obtenir_ventes()

            # Frame principal pour les statistiques
            main_stats_frame = ttk.Frame(self.content_frame)
            main_stats_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

            # Statistiques par module
            modules_frame = ttk.LabelFrame(main_stats_frame, text="📊 Résumé par Module", padding="10")
            modules_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

            # Données des modules
            modules_data = [
                ("📦 Réceptions", len(receptions), f"{sum(r.get('tonnage', 0) for r in receptions):,.0f} T"),
                ("🏭 Productions", len(productions), f"{sum(p.get('total_production_concentre', 0) for p in productions if p.get('total_production_concentre')):,.0f} T"),
                ("⚠️ Arrêts Laverie", len(arrets_laverie), f"{sum(a.get('total_heures_arret', 0) for a in arrets_laverie):,.0f} h"),
                ("⚠️ Arrêts Concentrateur", len(arrets_concentrateur), f"{sum(a.get('total_heures_arret', 0) for a in arrets_concentrateur):,.0f} h"),
                ("💰 Ventes", len(ventes), f"{sum(v.get('quantite_demandee', 0) for v in ventes):,.0f} T"),
            ]

            for i, (module, count, total) in enumerate(modules_data):
                ttk.Label(modules_frame, text=f"{module}:").grid(row=i, column=0, sticky=tk.W, pady=2)
                ttk.Label(modules_frame, text=f"{count} entrées", font=('Arial', 10, 'bold')).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)
                ttk.Label(modules_frame, text=total, font=('Arial', 10, 'bold')).grid(row=i, column=2, sticky=tk.W, padx=(20, 0), pady=2)

            # Indicateurs clés
            kpi_frame = ttk.LabelFrame(main_stats_frame, text="🎯 Indicateurs Clés", padding="10")
            kpi_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

            # Calculer les KPI
            # Rendement global
            total_reception = sum(p.get('total_reception_phosphate_brut', 0) for p in productions if p.get('total_reception_phosphate_brut'))
            total_production = sum(p.get('total_production_concentre', 0) for p in productions if p.get('total_production_concentre'))
            rendement_global = (total_production / total_reception * 100) if total_reception > 0 else 0

            # Disponibilité moyenne
            total_heures_marche = sum(a.get('total_heures_marche', 0) for a in arrets_laverie + arrets_concentrateur)
            total_heures_arret = sum(a.get('total_heures_arret', 0) for a in arrets_laverie + arrets_concentrateur)
            disponibilite = (total_heures_marche / (total_heures_marche + total_heures_arret) * 100) if (total_heures_marche + total_heures_arret) > 0 else 0

            # Qualité moyenne
            qualites = [r.get('p2o5_pourcentage') for r in receptions if r.get('p2o5_pourcentage')]
            qualite_moyenne = sum(qualites) / len(qualites) if qualites else 0

            # Taux de livraison
            ventes_livrees = len([v for v in ventes if v.get('statut_vente') == 'Livrée'])
            taux_livraison = (ventes_livrees / len(ventes) * 100) if ventes else 0

            kpi_data = [
                ("Rendement Global", f"{rendement_global:.1f}%", "🎯 Objectif: 75%"),
                ("Disponibilité Moyenne", f"{disponibilite:.1f}%", "🎯 Objectif: 85%"),
                ("Qualité P2O5 Moyenne", f"{qualite_moyenne:.2f}%", "🎯 Objectif: 29%"),
                ("Taux de Livraison", f"{taux_livraison:.1f}%", "🎯 Objectif: 95%"),
            ]

            for i, (kpi, valeur, objectif) in enumerate(kpi_data):
                ttk.Label(kpi_frame, text=f"{kpi}:").grid(row=i, column=0, sticky=tk.W, pady=2)
                ttk.Label(kpi_frame, text=valeur, font=('Arial', 12, 'bold')).grid(row=i, column=1, sticky=tk.W, padx=(20, 0), pady=2)
                ttk.Label(kpi_frame, text=objectif, font=('Arial', 8, 'italic')).grid(row=i, column=2, sticky=tk.W, padx=(20, 0), pady=2)

            # Alertes et recommandations
            alerts_frame = ttk.LabelFrame(main_stats_frame, text="⚠️ Alertes et Recommandations", padding="10")
            alerts_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

            alerts = []
            if rendement_global < 70:
                alerts.append("🔴 Rendement global faible - Vérifier les processus de production")
            if disponibilite < 80:
                alerts.append("🟡 Disponibilité en dessous de l'objectif - Planifier maintenance préventive")
            if qualite_moyenne < 28:
                alerts.append("🔴 Qualité P2O5 en dessous des standards - Contrôler la matière première")
            if taux_livraison < 90:
                alerts.append("🟡 Retards de livraison - Optimiser la logistique")

            if not alerts:
                alerts.append("✅ Tous les indicateurs sont dans les normes")

            for i, alert in enumerate(alerts):
                ttk.Label(alerts_frame, text=alert).grid(row=i, column=0, sticky=tk.W, pady=2)

            self.content_frame.rowconfigure(2, weight=1)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du calcul des statistiques: {e}")

    def show_trends_stats(self):
        """Afficher les tendances"""
        # Nettoyer le contenu existant (sauf titre et navigation)
        for widget in self.content_frame.winfo_children()[2:]:
            widget.destroy()

        trends_frame = ttk.LabelFrame(self.content_frame, text="📈 Analyse des Tendances", padding="10")
        trends_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        try:
            # Analyse mensuelle
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            productions = ProductionManager.obtenir_production()

            # Grouper par mois
            monthly_data = {}

            # Réceptions par mois
            for reception in receptions:
                try:
                    date_obj = datetime.strptime(str(reception['date_reception']), '%Y-%m-%d')
                    month_key = date_obj.strftime('%Y-%m')

                    if month_key not in monthly_data:
                        monthly_data[month_key] = {'receptions': 0, 'tonnage': 0, 'productions': 0}

                    monthly_data[month_key]['receptions'] += 1
                    monthly_data[month_key]['tonnage'] += reception.get('tonnage', 0)
                except:
                    continue

            # Productions par mois
            for production in productions:
                try:
                    date_obj = datetime.strptime(str(production['date_production']), '%Y-%m-%d')
                    month_key = date_obj.strftime('%Y-%m')

                    if month_key not in monthly_data:
                        monthly_data[month_key] = {'receptions': 0, 'tonnage': 0, 'productions': 0}

                    monthly_data[month_key]['productions'] += 1
                except:
                    continue

            # Afficher les tendances
            ttk.Label(trends_frame, text="📊 Évolution Mensuelle", font=('Arial', 12, 'bold')).grid(row=0, column=0, columnspan=4, pady=(0, 10))

            # En-têtes
            headers = ['Mois', 'Réceptions', 'Tonnage (T)', 'Productions']
            for i, header in enumerate(headers):
                ttk.Label(trends_frame, text=header, font=('Arial', 10, 'bold')).grid(row=1, column=i, padx=5, pady=2)

            # Données mensuelles
            for i, (month, data) in enumerate(sorted(monthly_data.items())[-6:]):  # 6 derniers mois
                ttk.Label(trends_frame, text=month).grid(row=i+2, column=0, padx=5, pady=1)
                ttk.Label(trends_frame, text=str(data['receptions'])).grid(row=i+2, column=1, padx=5, pady=1)
                ttk.Label(trends_frame, text=f"{data['tonnage']:,.0f}").grid(row=i+2, column=2, padx=5, pady=1)
                ttk.Label(trends_frame, text=str(data['productions'])).grid(row=i+2, column=3, padx=5, pady=1)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'analyse des tendances: {e}")

    def show_kpi_stats(self):
        """Afficher les KPI détaillés"""
        # Nettoyer le contenu existant (sauf titre et navigation)
        for widget in self.content_frame.winfo_children()[2:]:
            widget.destroy()

        kpi_frame = ttk.LabelFrame(self.content_frame, text="🎯 Indicateurs de Performance (KPI)", padding="10")
        kpi_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        messagebox.showinfo("KPI", "Module KPI détaillé en cours de développement")

    def generate_global_report(self):
        """Générer un rapport global"""
        try:
            from datetime import datetime

            # Créer le nom du fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"rapport_global_sotramine_{timestamp}.txt"

            # Générer le contenu du rapport
            report_content = f"""
RAPPORT GLOBAL SOTRAMINE
========================
Généré le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}

RÉSUMÉ EXÉCUTIF
===============
"""

            # Ajouter les données
            try:
                receptions = ReceptionPhosphateManager.obtenir_receptions()
                productions = ProductionManager.obtenir_production()
                ventes = VenteManager.obtenir_ventes()

                report_content += f"""
DONNÉES GÉNÉRALES
=================
- Réceptions: {len(receptions)} entrées
- Productions: {len(productions)} entrées
- Ventes: {len(ventes)} entrées

TONNAGES
========
- Réception totale: {sum(r.get('tonnage', 0) for r in receptions):,.0f} T
- Production totale: {sum(p.get('total_production_concentre', 0) for p in productions if p.get('total_production_concentre')):,.0f} T
- Ventes totales: {sum(v.get('quantite_demandee', 0) for v in ventes):,.0f} T

QUALITÉ
=======
- P2O5 moyen réceptions: {sum(r.get('p2o5_pourcentage', 0) for r in receptions if r.get('p2o5_pourcentage')) / len([r for r in receptions if r.get('p2o5_pourcentage')]):.2f}% (si données disponibles)

RECOMMANDATIONS
===============
- Continuer le suivi régulier des indicateurs
- Maintenir la qualité des analyses chimiques
- Optimiser les processus de production

Rapport généré par SOTRAMINE v1.0
"""

                # Sauvegarder le rapport
                reports_dir = "reports"
                if not os.path.exists(reports_dir):
                    os.makedirs(reports_dir)

                report_path = os.path.join(reports_dir, report_filename)
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write(report_content)

                messagebox.showinfo("Rapport généré", f"Rapport sauvegardé:\n{report_path}")

            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors de la génération du rapport: {e}")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur générale: {e}")

    def import_excel(self):
        """Importer un fichier Excel"""
        file_path = filedialog.askopenfilename(
            title="Sélectionner un fichier Excel",
            filetypes=[("Fichiers Excel", "*.xlsx *.xls"), ("Tous les fichiers", "*.*")]
        )

        if file_path:
            # Confirmer l'import
            response = messagebox.askyesno(
                "Confirmer l'import",
                f"Voulez-vous importer les données du fichier:\n{os.path.basename(file_path)}\n\nCela peut prendre quelques minutes."
            )

            if response:
                try:
                    # Afficher une fenêtre de progression
                    progress_window = tk.Toplevel(self.root)
                    progress_window.title("Import en cours...")
                    progress_window.geometry("400x150")
                    progress_window.resizable(False, False)

                    # Centrer la fenêtre
                    progress_window.transient(self.root)
                    progress_window.grab_set()

                    ttk.Label(progress_window, text="Import des données Excel en cours...", font=('Arial', 12)).pack(pady=20)

                    progress_bar = ttk.Progressbar(progress_window, mode='indeterminate')
                    progress_bar.pack(pady=10, padx=20, fill=tk.X)
                    progress_bar.start()

                    status_label = ttk.Label(progress_window, text="Initialisation...")
                    status_label.pack(pady=10)

                    # Forcer la mise à jour de l'affichage
                    progress_window.update()

                    # Importer les données
                    from excel_importer import ExcelImporter

                    status_label.config(text="Lecture du fichier Excel...")
                    progress_window.update()

                    importer = ExcelImporter(file_path)

                    status_label.config(text="Import des données...")
                    progress_window.update()

                    stats = importer.importer_tout()

                    # Fermer la fenêtre de progression
                    progress_bar.stop()
                    progress_window.destroy()

                    # Afficher les résultats
                    total_imported = (stats['receptions'] + stats['productions'] +
                                    stats['arrets_laverie'] + stats['arrets_concentrateur'] + stats['ventes'])

                    result_message = f"""Import terminé avec succès !

📊 Résultats:
• Réceptions: {stats['receptions']}
• Productions: {stats['productions']}
• Arrêts laverie: {stats['arrets_laverie']}
• Arrêts concentrateur: {stats['arrets_concentrateur']}
• Ventes: {stats['ventes']}

Total: {total_imported} enregistrements importés"""

                    if stats['erreurs']:
                        result_message += f"\n\n⚠️ {len(stats['erreurs'])} erreur(s) détectée(s)"

                    messagebox.showinfo("Import terminé", result_message)

                    # Actualiser l'affichage si on est sur le tableau de bord
                    self.show_dashboard()

                except Exception as e:
                    # Fermer la fenêtre de progression en cas d'erreur
                    try:
                        progress_bar.stop()
                        progress_window.destroy()
                    except:
                        pass

                    messagebox.showerror("Erreur d'import", f"Erreur lors de l'import:\n{str(e)}")

    def export_data(self):
        """Exporter les données"""
        messagebox.showinfo("Export", "Fonctionnalité d'export en cours de développement")

    def generate_monthly_report(self):
        """Générer un rapport mensuel"""
        messagebox.showinfo("Rapport", "Génération de rapport en cours de développement")

    def show_about(self):
        """Afficher les informations sur l'application"""
        app_info = get_app_info()
        about_text = f"""
{app_info['name']} v{app_info['version']}

{app_info['description']}

Développé par: {app_info['author']}
Dernière mise à jour: {app_info['last_updated']}

Python: {app_info['python_version']}
Dépendances: {', '.join(app_info['dependencies'])}
        """
        messagebox.showinfo("À propos", about_text)

    def show_config(self):
        """Afficher la fenêtre de configuration"""
        try:
            config_gui = ConfigGUI(self.root)
            config_gui.show_config_window()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ouverture de la configuration: {e}")

    def show_theme_selector(self):
        """Afficher le sélecteur de thèmes"""
        try:
            from theme_selector import ThemeSelector
            theme_selector = ThemeSelector(self)
            theme_selector.show_selector()
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ouverture du sélecteur de thèmes: {e}")

    def refresh_theme(self):
        """Rafraîchir l'application avec le nouveau thème"""
        try:
            # Réinitialiser le système de thèmes
            initialize_theme_system(self.root)
            self.theme_manager = get_theme_manager()
            self.modern_styles = get_modern_styles()
            self.modern_styles.configure_styles()

            # Appliquer le nouveau thème à la fenêtre
            theme = self.theme_manager.get_theme()
            self.root.configure(bg=theme["colors"]["background"])

            # Mettre à jour le statut
            self.update_status("Thème mis à jour", "success")

            messagebox.showinfo("Thème appliqué", "Le nouveau thème a été appliqué avec succès!")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'application du thème: {e}")

    def run_diagnostic(self):
        """Lancer le diagnostic système"""
        try:
            import subprocess
            import sys

            # Lancer le script de diagnostic
            result = subprocess.run([sys.executable, "diagnostic.py"],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                messagebox.showinfo("Diagnostic", "Diagnostic terminé avec succès!\nConsultez la console pour les détails.")
            else:
                messagebox.showwarning("Diagnostic", "Diagnostic terminé avec des avertissements.\nConsultez la console pour les détails.")

        except subprocess.TimeoutExpired:
            messagebox.showerror("Erreur", "Le diagnostic a pris trop de temps.")
        except FileNotFoundError:
            messagebox.showerror("Erreur", "Script de diagnostic non trouvé.")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors du diagnostic: {e}")

    def backup_database(self):
        """Sauvegarder la base de données"""
        try:
            import shutil
            from datetime import datetime

            # Créer le nom de fichier de sauvegarde
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"sotramine_backup_{timestamp}.db"

            # Créer le dossier de sauvegarde s'il n'existe pas
            backup_dir = "backups"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            backup_path = os.path.join(backup_dir, backup_filename)

            # Copier la base de données
            shutil.copy2("sotramine_phosphate.db", backup_path)

            messagebox.showinfo("Sauvegarde", f"Base de données sauvegardée avec succès:\n{backup_path}")

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def show_help(self):
        """Afficher l'aide"""
        use_emojis = self.config_manager.get_value('ui', 'use_emojis', True)

        if use_emojis:
            help_text = """
🏭 AIDE SOTRAMINE

Navigation:
• Utilisez le menu de gauche pour naviguer entre les modules
• Le tableau de bord affiche un résumé des données
• Chaque module permet de gérer un type de données spécifique

Modules disponibles:
📦 Réceptions - Gestion des réceptions de phosphate
🏭 Production - Suivi de la production journalière
⚠️ Arrêts - Gestion des arrêts et maintenance
💰 Ventes - Planning et suivi des ventes

Fonctionnalités:
📊 Import Excel - Importer des données depuis Excel
📈 Statistiques - Analyses et rapports
📄 Export - Exporter les données
⚙️ Configuration - Personnaliser l'application

Pour plus d'aide, consultez le fichier README.md
            """
        else:
            help_text = """
AIDE SOTRAMINE

Navigation:
• Utilisez le menu de gauche pour naviguer entre les modules
• Le tableau de bord affiche un résumé des données
• Chaque module permet de gérer un type de données spécifique

Modules disponibles:
• Réceptions - Gestion des réceptions de phosphate
• Production - Suivi de la production journalière
• Arrêts - Gestion des arrêts et maintenance
• Ventes - Planning et suivi des ventes

Fonctionnalités:
• Import Excel - Importer des données depuis Excel
• Statistiques - Analyses et rapports
• Export - Exporter les données
• Configuration - Personnaliser l'application

Pour plus d'aide, consultez le fichier README.md
            """

        messagebox.showinfo("Aide", help_text)

    def run(self):
        """Lancer l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    try:
        app = SotramineGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Erreur fatale", f"Erreur lors du lancement de l'application: {e}")

if __name__ == "__main__":
    main()
