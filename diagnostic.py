#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de diagnostic pour l'application SOTRAMINE
Aide à identifier et résoudre les problèmes
"""

import os
import sys
import sqlite3
from datetime import datetime
import traceback

def print_header(title):
    """Afficher un en-tête formaté"""
    print("\n" + "=" * 60)
    print(f"🔧 {title}")
    print("=" * 60)

def check_python_version():
    """Vérifier la version de Python"""
    print_header("VÉRIFICATION PYTHON")
    
    version = sys.version_info
    print(f"✅ Version Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("⚠️ ATTENTION: Python 3.7+ recommandé")
        return False
    else:
        print("✅ Version Python compatible")
        return True

def check_dependencies():
    """Vérifier les dépendances"""
    print_header("VÉRIFICATION DES DÉPENDANCES")
    
    dependencies = {
        'tkinter': 'Interface graphique',
        'sqlite3': 'Base de données',
        'pandas': 'Traitement des données',
        'openpyxl': 'Lecture Excel',
        'datetime': 'Gestion des dates',
        'os': 'Système de fichiers',
        'sys': 'Système'
    }
    
    missing = []
    
    for module, description in dependencies.items():
        try:
            __import__(module)
            print(f"✅ {module:<12} - {description}")
        except ImportError:
            print(f"❌ {module:<12} - {description} (MANQUANT)")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️ Modules manquants: {', '.join(missing)}")
        print("📦 Pour installer: pip install pandas openpyxl")
        return False
    else:
        print("\n✅ Toutes les dépendances sont installées")
        return True

def check_files():
    """Vérifier la présence des fichiers"""
    print_header("VÉRIFICATION DES FICHIERS")
    
    required_files = {
        'SOTRAMINE.py': 'Lanceur principal',
        'gui_main.py': 'Interface graphique',
        'gui_dialogs.py': 'Fenêtres de dialogue',
        'database_models.py': 'Modèles de base de données',
        'data_managers.py': 'Gestionnaires de données',
        'config.py': 'Configuration',
        'excel_importer.py': 'Import Excel'
    }
    
    optional_files = {
        'sotramine_phosphate.db': 'Base de données',
        'Feuille de suivi phosphate SOTRAMINE_V0.4 final.xlsx': 'Fichier Excel original',
        'README.md': 'Documentation',
        'GUIDE_UTILISATION.md': 'Guide utilisateur'
    }
    
    missing_required = []
    
    print("📋 Fichiers requis:")
    for file, description in required_files.items():
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file:<25} - {description} ({size} bytes)")
        else:
            print(f"❌ {file:<25} - {description} (MANQUANT)")
            missing_required.append(file)
    
    print("\n📋 Fichiers optionnels:")
    for file, description in optional_files.items():
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file:<45} - {description} ({size} bytes)")
        else:
            print(f"⚠️ {file:<45} - {description} (absent)")
    
    if missing_required:
        print(f"\n❌ Fichiers requis manquants: {', '.join(missing_required)}")
        return False
    else:
        print("\n✅ Tous les fichiers requis sont présents")
        return True

def check_database():
    """Vérifier la base de données"""
    print_header("VÉRIFICATION BASE DE DONNÉES")
    
    db_file = "sotramine_phosphate.db"
    
    if not os.path.exists(db_file):
        print(f"⚠️ Base de données non trouvée: {db_file}")
        print("🔧 Tentative de création...")
        
        try:
            from database_models import DatabaseManager
            db = DatabaseManager()
            print("✅ Base de données créée avec succès")
            return True
        except Exception as e:
            print(f"❌ Erreur lors de la création: {e}")
            return False
    
    try:
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # Vérifier les tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        expected_tables = [
            'reception_phosphate', 'production_journaliere', 'arrets_laverie',
            'arrets_concentrateur', 'ventes', 'bilans_journaliers', 'inventaires'
        ]
        
        print(f"📊 Tables trouvées: {len(tables)}")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  • {table}: {count} enregistrements")
        
        missing_tables = [t for t in expected_tables if t not in tables]
        if missing_tables:
            print(f"⚠️ Tables manquantes: {', '.join(missing_tables)}")
        
        conn.close()
        print("✅ Base de données accessible")
        return True
        
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def check_permissions():
    """Vérifier les permissions"""
    print_header("VÉRIFICATION DES PERMISSIONS")
    
    current_dir = os.getcwd()
    print(f"📁 Répertoire courant: {current_dir}")
    
    # Test d'écriture
    test_file = "test_permissions.tmp"
    try:
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("✅ Permissions d'écriture: OK")
        write_ok = True
    except Exception as e:
        print(f"❌ Permissions d'écriture: {e}")
        write_ok = False
    
    # Test de lecture
    try:
        files = os.listdir('.')
        print(f"✅ Permissions de lecture: OK ({len(files)} fichiers)")
        read_ok = True
    except Exception as e:
        print(f"❌ Permissions de lecture: {e}")
        read_ok = False
    
    return read_ok and write_ok

def test_imports():
    """Tester les imports des modules de l'application"""
    print_header("TEST DES IMPORTS")
    
    modules_to_test = [
        ('database_models', 'Modèles de base de données'),
        ('data_managers', 'Gestionnaires de données'),
        ('config', 'Configuration'),
        ('gui_main', 'Interface graphique'),
        ('gui_dialogs', 'Fenêtres de dialogue')
    ]
    
    import_errors = []
    
    for module_name, description in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name:<20} - {description}")
        except Exception as e:
            print(f"❌ {module_name:<20} - {description} (ERREUR: {e})")
            import_errors.append((module_name, str(e)))
    
    if import_errors:
        print(f"\n❌ Erreurs d'import détectées:")
        for module, error in import_errors:
            print(f"  • {module}: {error}")
        return False
    else:
        print("\n✅ Tous les imports fonctionnent")
        return True

def test_gui_launch():
    """Tester le lancement de l'interface graphique"""
    print_header("TEST INTERFACE GRAPHIQUE")
    
    try:
        import tkinter as tk
        
        # Test simple de tkinter
        root = tk.Tk()
        root.withdraw()  # Cacher la fenêtre
        root.destroy()
        
        print("✅ Tkinter fonctionne")
        
        # Test d'import de l'interface
        from gui_main import SotramineGUI
        print("✅ Import de l'interface réussi")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur interface graphique: {e}")
        print(f"📋 Détails: {traceback.format_exc()}")
        return False

def generate_report():
    """Générer un rapport de diagnostic"""
    print_header("RAPPORT DE DIAGNOSTIC")
    
    results = {
        'Python': check_python_version(),
        'Dépendances': check_dependencies(),
        'Fichiers': check_files(),
        'Base de données': check_database(),
        'Permissions': check_permissions(),
        'Imports': test_imports(),
        'Interface graphique': test_gui_launch()
    }
    
    print("\n📊 RÉSUMÉ:")
    all_ok = True
    for test, result in results.items():
        status = "✅ OK" if result else "❌ PROBLÈME"
        print(f"  • {test:<20}: {status}")
        if not result:
            all_ok = False
    
    if all_ok:
        print("\n🎉 DIAGNOSTIC COMPLET: Tout fonctionne correctement !")
        print("🚀 L'application devrait se lancer sans problème.")
    else:
        print("\n⚠️ DIAGNOSTIC: Des problèmes ont été détectés.")
        print("🔧 Corrigez les erreurs ci-dessus avant de lancer l'application.")
    
    return all_ok

def main():
    """Fonction principale"""
    print("🏭 SOTRAMINE - Diagnostic Système")
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        success = generate_report()
        
        print("\n" + "=" * 60)
        if success:
            print("✅ Diagnostic terminé: Système prêt")
        else:
            print("❌ Diagnostic terminé: Problèmes détectés")
        print("=" * 60)
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"\n❌ Erreur lors du diagnostic: {e}")
        print(f"📋 Détails: {traceback.format_exc()}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    input("\nAppuyez sur Entrée pour fermer...")
    sys.exit(exit_code)
