#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Modèles de base de données pour l'application SOTRAMINE
Suivi des opérations de phosphate
"""

import sqlite3
import os
from datetime import datetime
from typing import Optional, List, Dict, Any

class DatabaseManager:
    """Gestionnaire de base de données SQLite pour SOTRAMINE"""
    
    def __init__(self, db_path: str = "sotramine_phosphate.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """Initialiser la base de données avec toutes les tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Table: Réception Phosphate
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS reception_phosphate (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                numero_voyage INTEGER NOT NULL,
                date_reception DATE NOT NULL,
                numero_bl_camion TEXT,
                serie_camion TEXT,
                tonnage REAL,
                p2o5_pourcentage REAL,
                cao_pourcentage REAL,
                mgo_pourcentage REAL,
                sio2_pourcentage REAL,
                observations TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table: Production Journalière
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS production_journaliere (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_production DATE NOT NULL,
                annee INTEGER,
                mois INTEGER,
                jour INTEGER,
                regime_travail INTEGER DEFAULT 8,
                total_reception_phosphate_brut REAL,
                total_consommation_concentre REAL,
                total_production_concentre REAL,
                total_heures_marche REAL,
                total_heures_arret REAL,
                total_consommation_lavage_ph REAL,
                total_production_lavage_ph REAL,
                total_heures_marche_lavage REAL,
                total_heures_arret_lavage REAL,
                total_consommation_electricite REAL,
                total_consommation_eau REAL,
                total_consommation_floculant REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table: Arrêts Laverie
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS arrets_laverie (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_arret DATE NOT NULL,
                annee INTEGER,
                mois INTEGER,
                jour INTEGER,
                total_heures_marche REAL DEFAULT 0,
                total_heures_arret REAL DEFAULT 0,
                cause_arret_1 TEXT,
                duree_arret_1 REAL,
                cause_arret_2 TEXT,
                duree_arret_2 REAL,
                cause_arret_3 TEXT,
                duree_arret_3 REAL,
                erreur_signale BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table: Arrêts Concentrateur
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS arrets_concentrateur (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_arret DATE NOT NULL,
                annee INTEGER,
                mois INTEGER,
                jour INTEGER,
                total_heures_marche REAL DEFAULT 0,
                total_heures_arret REAL DEFAULT 0,
                cause_arret_1 TEXT,
                duree_arret_1 REAL,
                cause_arret_2 TEXT,
                duree_arret_2 REAL,
                cause_arret_3 TEXT,
                duree_arret_3 REAL,
                erreur_signale BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table: Ventes
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ventes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_vente DATE NOT NULL,
                numero_commande INTEGER,
                client TEXT NOT NULL,
                date_cargaison DATE,
                quantite_demandee REAL NOT NULL,
                qualite_p2o5_pourcentage REAL,
                statut_vente TEXT DEFAULT 'Planifiée',
                observations TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table: Bilans Journaliers
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bilans_journaliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_bilan DATE NOT NULL,
                responsable TEXT NOT NULL,
                usine TEXT DEFAULT 'BOUGRINE / USINE LAVERIE DE PHOSPHATE',
                production_totale REAL,
                consommation_totale REAL,
                rendement_pourcentage REAL,
                incidents_signales TEXT,
                observations TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Table: Inventaires
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventaires (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date_debut DATE NOT NULL,
                date_fin DATE NOT NULL,
                type_inventaire TEXT DEFAULT 'Mensuel',
                stock_initial REAL,
                stock_final REAL,
                entrees_totales REAL,
                sorties_totales REAL,
                ecart_inventaire REAL,
                observations TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Base de données initialisée avec succès!")
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """Exécuter une requête SELECT et retourner les résultats"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        
        # Obtenir les noms des colonnes
        columns = [description[0] for description in cursor.description]
        
        # Convertir les résultats en liste de dictionnaires
        results = []
        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))
        
        conn.close()
        return results
    
    def execute_insert(self, query: str, params: tuple = ()) -> int:
        """Exécuter une requête INSERT et retourner l'ID du nouvel enregistrement"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        last_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return last_id
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """Exécuter une requête UPDATE et retourner le nombre de lignes affectées"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(query, params)
        affected_rows = cursor.rowcount
        conn.commit()
        conn.close()
        return affected_rows
    
    def get_table_info(self, table_name: str) -> List[Dict]:
        """Obtenir les informations sur une table"""
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)
    
    def get_all_tables(self) -> List[str]:
        """Obtenir la liste de toutes les tables"""
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
        results = self.execute_query(query)
        return [result['name'] for result in results]

# Instance globale du gestionnaire de base de données
db_manager = DatabaseManager()

if __name__ == "__main__":
    # Test de la base de données
    print("🔧 Test du gestionnaire de base de données SOTRAMINE")
    print(f"📁 Fichier de base de données: {db_manager.db_path}")
    
    # Afficher toutes les tables
    tables = db_manager.get_all_tables()
    print(f"📊 Tables créées: {tables}")
    
    # Afficher la structure d'une table
    if tables:
        table_info = db_manager.get_table_info(tables[0])
        print(f"\n🏗️ Structure de la table '{tables[0]}':")
        for column in table_info:
            print(f"  • {column['name']}: {column['type']}")
