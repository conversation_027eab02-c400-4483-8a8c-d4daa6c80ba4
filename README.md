# 🏭 Application SOTRAMINE - Suivi Phosphate

Application Python complète pour la gestion et le suivi des opérations de phosphate de l'usine SOTRAMINE.

## 📋 Description

Cette application remplace le fichier Excel de suivi par une solution Python robuste avec base de données SQLite, permettant une gestion efficace des données opérationnelles de l'usine de traitement de phosphate.

## ✨ Fonctionnalités

### 📦 Gestion Réception Phosphate
- Enregistrement des réceptions de phosphate brut
- Suivi des voyages, tonnages et analyses chimiques (P2O5, CaO, MgO, SiO2)
- Statistiques par période (mensuel, annuel)
- Filtrage par dates

### 🏭 Gestion Production
- Suivi de la production journalière
- Données de consommation (électricité, eau, floculant)
- Calcul automatique des rendements
- Heures de marche et d'arrêt

### ⚠️ Gestion des Arrêts
- Suivi des arrêts laverie et concentrateur
- Causes et durées des arrêts
- Calcul des taux de disponibilité
- Statistiques d'arrêts

### 💰 Gestion des Ventes
- Planning des ventes et navires
- Suivi des commandes clients
- Qualité et quantités demandées
- Statuts des ventes

### 📊 Bilans et Rapports
- Bilans journaliers automatisés
- Inventaires mensuels
- Rapports de production
- Calculs d'écarts

### 📈 Statistiques et Analyses
- Tableaux de bord interactifs
- Analyses de tendances
- Indicateurs de performance (KPI)
- Rapports personnalisés

## 🚀 Installation et Lancement

### 🎯 Lancement Rapide (Recommandé)
1. **Double-cliquez** sur `Lancer_SOTRAMINE.bat` (Windows)
2. Ou **double-cliquez** sur `SOTRAMINE.py`
3. L'application vérifie et installe automatiquement les dépendances

### 🔧 Installation Manuelle
Si nécessaire, installez les dépendances manuellement :
```bash
pip install pandas openpyxl
```

### 📋 Prérequis
- Python 3.7 ou supérieur
- pandas (installation automatique)
- openpyxl (installation automatique)

## 📁 Structure du Projet

```
sotramine-phosphate/
├── 🚀 SOTRAMINE.py                    # 🎯 LANCEUR PRINCIPAL
├── 🖥️ Lancer_SOTRAMINE.bat           # Lanceur Windows (double-clic)
├── 🎨 gui_main.py                    # Interface graphique principale
├── 📝 gui_dialogs.py                 # Fenêtres de dialogue
├── 🗄️ database_models.py             # Modèles de base de données
├── 🔧 data_managers.py               # Gestionnaires de données
├── 📊 excel_importer.py              # Import depuis Excel
├── ⚙️ config.py                      # Configuration
├── 🧪 test_app.py                    # Tests unitaires
├── 📖 GUIDE_UTILISATION.md           # Guide d'utilisation
├── 💾 sotramine_phosphate.db         # Base de données SQLite
└── 📋 README.md                      # Documentation technique
```

## 🎯 Utilisation

### 🖥️ Interface Graphique (Recommandée)
```bash
# Méthode 1: Double-clic sur le fichier
Lancer_SOTRAMINE.bat    # Windows
SOTRAMINE.py           # Tous systèmes

# Méthode 2: Ligne de commande
python SOTRAMINE.py
```

### 📱 Interface en Ligne de Commande
```bash
python sotramine_app.py
```

### 📊 Import des Données Excel
- **Via l'interface graphique** : Menu "📁 Fichier > 📊 Importer Excel"
- **Via ligne de commande** : `python excel_importer.py`

## 🗄️ Base de Données

L'application utilise SQLite avec les tables suivantes :

- **reception_phosphate** : Réceptions de phosphate brut
- **production_journaliere** : Données de production quotidienne
- **arrets_laverie** : Arrêts de la laverie
- **arrets_concentrateur** : Arrêts du concentrateur
- **ventes** : Planning et suivi des ventes
- **bilans_journaliers** : Bilans quotidiens
- **inventaires** : Inventaires périodiques

## 📊 Données Importées

L'application a importé avec succès :
- ✅ **152 réceptions** de phosphate
- ✅ **100 entrées de production**
- ✅ **100 arrêts laverie**
- ✅ **100 arrêts concentrateur**
- ✅ **1 vente** planifiée

## 🔧 Fonctionnalités Techniques

### Gestionnaire de Base de Données
- Connexions SQLite optimisées
- Requêtes paramétrées sécurisées
- Gestion automatique des transactions
- Sauvegarde et restauration

### Import/Export
- Import depuis fichiers Excel (.xlsx)
- Export vers CSV
- Validation des données
- Gestion des erreurs

### Interface Utilisateur
- Interface en ligne de commande intuitive
- Menus interactifs avec émojis
- Validation des saisies
- Messages d'erreur explicites

## ✨ Nouvelles Fonctionnalités - Interface Graphique

### 🖥️ Interface Moderne et Intuitive
- **🎨 Design professionnel** avec thème moderne
- **🧭 Navigation simplifiée** avec menu latéral
- **📊 Tableau de bord** avec statistiques en temps réel
- **🔄 Actualisation automatique** des données

### 🚀 Fonctionnalités Avancées
- **➕ Ajout facile** via formulaires intuitifs
- **✏️ Modification rapide** par double-clic
- **📈 Statistiques visuelles** avec graphiques
- **📊 Import Excel** avec barre de progression
- **🔍 Filtrage et tri** des données
- **💾 Sauvegarde automatique**

### 🎯 Modules Intégrés
- **📦 Réceptions** : Gestion complète avec analyses chimiques
- **🏭 Production** : Calculs de rendement automatiques
- **⚠️ Arrêts** : Suivi des maintenances
- **💰 Ventes** : Planning et suivi client
- **📈 Statistiques** : Analyses détaillées

### 🎯 Avantages par rapport à Excel
1. **🖥️ Interface graphique moderne** : Plus intuitive qu'Excel
2. **🔒 Intégrité des données** : Base de données relationnelle
3. **📊 Calculs automatiques** : Rendements, statistiques, KPI
4. **🔍 Recherche avancée** : Filtres par date, client, etc.
5. **📈 Analyses en temps réel** : Pas besoin de formules Excel
6. **🚀 Performance** : Traitement rapide de gros volumes
7. **💾 Sauvegarde sécurisée** : Pas de risque de corruption
8. **🔧 Extensibilité** : Facile d'ajouter de nouvelles fonctionnalités

## 📈 Exemples d'Utilisation

### Consulter les statistiques mensuelles
```
Menu Principal > Gestion Réception Phosphate > Statistiques réceptions
Mois: 2
Année: 2025

Résultats:
🔢 Nombre de réceptions: 69
⚖️ Tonnage total: 2,280,776.00 T
📊 Tonnage moyen: 33,054.72 T
```

### Ajouter une nouvelle réception
```
Menu Principal > Gestion Réception Phosphate > Ajouter une réception
Numéro de voyage: 153
Date: 2025-01-28
Tonnage: 35000
P2O5 %: 29.5
```

## 🛠️ Développement

### Architecture
- **Modèle MVC** : Séparation claire des responsabilités
- **Gestionnaires de données** : Classes spécialisées par domaine
- **Base de données relationnelle** : Structure normalisée
- **Modularité** : Code réutilisable et maintenable

### Extensibilité
- Ajout facile de nouveaux modules
- Interface standardisée pour les gestionnaires
- Structure de base de données évolutive
- Configuration centralisée

## 🔒 Sécurité

- Requêtes SQL paramétrées (protection contre l'injection SQL)
- Validation des types de données
- Gestion des erreurs robuste
- Sauvegarde automatique des données

## 📝 Maintenance

### Sauvegarde
La base de données SQLite (`sotramine_phosphate.db`) doit être sauvegardée régulièrement.

### Mise à jour
Pour ajouter de nouvelles fonctionnalités, modifier les fichiers correspondants :
- `database_models.py` : Nouvelles tables
- `data_managers.py` : Nouvelle logique métier
- `sotramine_app.py` : Nouveaux menus

## 🤝 Support

Pour toute question ou problème :
1. Vérifier les logs d'erreur
2. Consulter la documentation du code
3. Tester avec des données d'exemple

## 📄 Licence

Application développée pour SOTRAMINE - Usage interne uniquement.

---

**Développé avec ❤️ pour optimiser les opérations de suivi phosphate SOTRAMINE**
