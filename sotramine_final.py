#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏭 SOTRAMINE - Version Finale
Système de Suivi Phosphate - Interface Professionnelle Complète
Version: 1.0 Final
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime
import sqlite3

# Imports des modules SOTRAMINE
try:
    from database_models import DatabaseManager
    from data_managers import (
        ReceptionPhosphateManager, ProductionManager, ArretManager,
        VenteManager, BilanManager
    )
    from config import get_config, get_app_info
    from themes import ThemeManager, ModernStyles, initialize_theme_system
except ImportError as e:
    print(f"⚠️ Modules manquants: {e}")

class SotramineApp:
    """Application SOTRAMINE - Version Finale"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_application()
        self.create_interface()
        self.load_data()

    def setup_application(self):
        """Configuration initiale de l'application"""
        # Configuration de base
        app_info = get_app_info()
        self.root.title(f"🏭 {app_info['name']} v{app_info['version']} - Version Finale")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)

        # Initialiser le système de thèmes
        try:
            initialize_theme_system(self.root)
            self.theme_manager = ThemeManager()
            self.theme = self.theme_manager.get_theme()
            self.root.configure(bg=self.theme["colors"]["background"])
        except:
            self.theme_manager = None
            self.theme = None
            self.root.configure(bg='#f0f0f0')

        # Variables d'état
        self.status_var = tk.StringVar(value="✅ Application initialisée")
        self.current_module = "dashboard"

        # Centrer la fenêtre
        self.center_window()

        # Configuration du grid principal
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # Créer le menu
        self.create_menu()

    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - 700
        y = (self.root.winfo_screenheight() // 2) - 450
        self.root.geometry(f"+{x}+{y}")

    def create_menu(self):
        """Créer la barre de menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📁 Fichier", menu=file_menu)
        file_menu.add_command(label="📊 Importer Excel", command=self.import_excel)
        file_menu.add_command(label="📤 Exporter données", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="❌ Quitter", command=self.quit_app)

        # Menu Outils
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="🔧 Outils", menu=tools_menu)
        tools_menu.add_command(label="⚙️ Configuration", command=self.show_config)
        tools_menu.add_command(label="🎨 Thèmes", command=self.show_themes)
        tools_menu.add_command(label="🔍 Diagnostic", command=self.run_diagnostic)
        tools_menu.add_separator()
        tools_menu.add_command(label="💾 Sauvegarde", command=self.backup_database)

        # Menu Aide
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="❓ Aide", menu=help_menu)
        help_menu.add_command(label="📖 Guide", command=self.show_help)
        help_menu.add_command(label="ℹ️ À propos", command=self.show_about)

    def create_interface(self):
        """Créer l'interface principale"""
        # Frame principal
        self.main_frame = tk.Frame(self.root)
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(1, weight=1)

        # En-tête
        self.create_header()

        # Navigation et contenu
        self.create_navigation()
        self.create_content_area()

        # Barre de statut
        self.create_status_bar()

    def create_header(self):
        """Créer l'en-tête professionnel"""
        if self.theme:
            bg_color = self.theme["colors"]["primary"]
            fg_color = "white"
        else:
            bg_color = "#1e3a8a"
            fg_color = "white"

        header_frame = tk.Frame(self.main_frame, bg=bg_color, height=80)
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 20))
        header_frame.grid_propagate(False)

        # Logo et titre
        title_label = tk.Label(
            header_frame,
            text="🏭 SOTRAMINE",
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 20, "bold")
        )
        title_label.place(x=20, y=15)

        # Sous-titre
        subtitle_label = tk.Label(
            header_frame,
            text="Système de Suivi Phosphate - Version Finale",
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 11)
        )
        subtitle_label.place(x=250, y=25)

        # Informations système
        app_info = get_app_info()
        info_label = tk.Label(
            header_frame,
            text=f"v{app_info['version']} | {datetime.now().strftime('%d/%m/%Y')}",
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 9)
        )
        info_label.place(relx=1.0, x=-20, y=25, anchor="ne")

    def create_navigation(self):
        """Créer la navigation moderne"""
        nav_frame = ttk.LabelFrame(
            self.main_frame,
            text="📋 Navigation",
            padding="15"
        )
        nav_frame.grid(row=1, column=0, sticky="ns", padx=(0, 15))

        # Modules principaux
        modules = [
            ("📊", "Tableau de bord", "dashboard", self.show_dashboard),
            ("📦", "Réceptions", "receptions", self.show_receptions),
            ("🏭", "Production", "production", self.show_production),
            ("⚠️", "Arrêts", "arrets", self.show_arrets),
            ("💰", "Ventes", "ventes", self.show_ventes),
            ("📈", "Statistiques", "stats", self.show_statistics),
            ("📊", "Import Excel", "import", self.import_excel),
        ]

        self.nav_buttons = {}
        for i, (icon, text, module_id, command) in enumerate(modules):
            btn = tk.Button(
                nav_frame,
                text=f"{icon} {text}",
                command=lambda cmd=command, mid=module_id: self.switch_module(cmd, mid),
                width=18,
                anchor="w",
                relief="flat",
                padx=12,
                pady=10,
                cursor="hand2",
                font=("Segoe UI", 10)
            )
            btn.grid(row=i, column=0, pady=2, sticky="ew")
            self.nav_buttons[module_id] = btn

            # Style du bouton
            self.style_nav_button(btn, module_id == "dashboard")

    def style_nav_button(self, button, is_active=False):
        """Appliquer le style aux boutons de navigation"""
        if self.theme:
            if is_active:
                button.configure(
                    bg=self.theme["colors"]["primary"],
                    fg="white",
                    activebackground=self.theme["colors"]["secondary"]
                )
            else:
                button.configure(
                    bg=self.theme["colors"]["surface"],
                    fg=self.theme["colors"]["text_primary"],
                    activebackground=self.theme["colors"]["secondary"]
                )
        else:
            if is_active:
                button.configure(bg="#1e3a8a", fg="white")
            else:
                button.configure(bg="#f1f5f9", fg="#1e293b")

    def switch_module(self, command, module_id):
        """Changer de module"""
        # Mettre à jour les styles des boutons
        for mid, btn in self.nav_buttons.items():
            self.style_nav_button(btn, mid == module_id)

        # Exécuter la commande
        self.current_module = module_id
        command()

    def create_content_area(self):
        """Créer la zone de contenu"""
        self.content_frame = ttk.LabelFrame(
            self.main_frame,
            text="📄 Contenu Principal",
            padding="20"
        )
        self.content_frame.grid(row=1, column=1, sticky="nsew")
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)

        # Afficher le tableau de bord par défaut
        self.show_dashboard()

    def create_status_bar(self):
        """Créer la barre de statut"""
        if self.theme:
            bg_color = self.theme["colors"]["surface"]
            fg_color = self.theme["colors"]["text_primary"]
        else:
            bg_color = "#e5e7eb"
            fg_color = "#374151"

        status_frame = tk.Frame(
            self.main_frame,
            bg=bg_color,
            height=30,
            relief="solid",
            borderwidth=1
        )
        status_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(20, 0))
        status_frame.grid_propagate(False)

        # Statut
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 9)
        )
        status_label.place(x=15, y=6)

        # Horloge
        self.time_var = tk.StringVar()
        time_label = tk.Label(
            status_frame,
            textvariable=self.time_var,
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 9)
        )
        time_label.place(relx=1.0, x=-15, y=6, anchor="ne")

        # Informations système
        try:
            if os.path.exists("sotramine_phosphate.db"):
                db_status = "💾 BD Connectée"
            else:
                db_status = "⚠️ BD Manquante"
        except:
            db_status = "❌ BD Erreur"

        db_label = tk.Label(
            status_frame,
            text=db_status,
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 9)
        )
        db_label.place(relx=0.5, anchor="n", y=6)

        # Démarrer l'horloge
        self.update_clock()

    def update_clock(self):
        """Mettre à jour l'horloge"""
        self.time_var.set(datetime.now().strftime("%H:%M:%S"))
        self.root.after(1000, self.update_clock)

    def clear_content(self):
        """Vider la zone de contenu"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def update_status(self, message, icon="ℹ️"):
        """Mettre à jour le statut"""
        self.status_var.set(f"{icon} {message}")

    def show_dashboard(self):
        """Afficher le tableau de bord"""
        self.clear_content()
        self.update_status("Tableau de bord chargé", "📊")

        # Titre
        title = tk.Label(
            self.content_frame,
            text="📊 Tableau de Bord Exécutif",
            font=("Segoe UI", 18, "bold"),
            fg="#1e3a8a" if self.theme else "#333"
        )
        title.grid(row=0, column=0, pady=(0, 30))

        # Statistiques rapides
        self.create_dashboard_stats()

        # Activité récente
        self.create_recent_activity()

    def create_dashboard_stats(self):
        """Créer les statistiques du tableau de bord"""
        stats_frame = tk.Frame(self.content_frame)
        stats_frame.grid(row=1, column=0, sticky="ew", pady=(0, 30))

        try:
            # Obtenir les données
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            productions = ProductionManager.obtenir_production()
            ventes = VenteManager.obtenir_ventes()

            # Calculer les statistiques
            stats = [
                ("📦", "Réceptions", len(receptions), "#3b82f6"),
                ("🏭", "Productions", len(productions), "#10b981"),
                ("💰", "Ventes", len(ventes), "#f59e0b"),
                ("⚖️", "Tonnage Total", f"{sum(r.get('tonnage', 0) for r in receptions):,.0f} T", "#ef4444")
            ]

            for i, (icon, title, value, color) in enumerate(stats):
                self.create_stat_card(stats_frame, icon, title, str(value), color, i)

        except Exception as e:
            error_label = tk.Label(
                stats_frame,
                text=f"⚠️ Erreur lors du chargement des statistiques: {e}",
                font=("Segoe UI", 10),
                fg="#ef4444"
            )
            error_label.grid(row=0, column=0, pady=20)

    def create_stat_card(self, parent, icon, title, value, color, position):
        """Créer une carte de statistique"""
        card = tk.Frame(
            parent,
            bg="white",
            relief="solid",
            borderwidth=1,
            padx=20,
            pady=15
        )
        card.grid(row=position//2, column=position%2, padx=10, pady=10, sticky="ew")

        # En-tête coloré
        header = tk.Frame(card, bg=color, height=4)
        header.pack(fill=tk.X, pady=(0, 10))

        # Icône et titre
        title_frame = tk.Frame(card, bg="white")
        title_frame.pack(fill=tk.X)

        icon_label = tk.Label(
            title_frame,
            text=icon,
            bg="white",
            font=("Segoe UI", 20),
            fg=color
        )
        icon_label.pack(side=tk.LEFT)

        title_label = tk.Label(
            title_frame,
            text=title,
            bg="white",
            fg="#374151",
            font=("Segoe UI", 11, "bold")
        )
        title_label.pack(side=tk.LEFT, padx=(10, 0))

        # Valeur
        value_label = tk.Label(
            card,
            text=value,
            bg="white",
            fg=color,
            font=("Segoe UI", 24, "bold")
        )
        value_label.pack(pady=(5, 0))

        parent.columnconfigure(position%2, weight=1)

    def create_recent_activity(self):
        """Créer la section d'activité récente"""
        activity_frame = ttk.LabelFrame(
            self.content_frame,
            text="📈 Activité Récente",
            padding="15"
        )
        activity_frame.grid(row=2, column=0, sticky="ew", pady=(0, 20))

        # Message d'information
        info_text = """
🎉 Bienvenue dans SOTRAMINE - Version Finale

✨ Fonctionnalités disponibles :
• 📦 Gestion complète des réceptions de phosphate
• 🏭 Suivi détaillé de la production journalière
• ⚠️ Gestion des arrêts et maintenance (laverie/concentrateur)
• 💰 Planning et suivi complet des ventes
• 📈 Statistiques et analyses avancées avec KPI
• 🎨 Système de thèmes professionnels personnalisables

🚀 Utilisez la navigation de gauche pour accéder aux modules.
📊 Importez vos données Excel via le menu Fichier.
⚙️ Personnalisez l'interface via le menu Outils.
        """

        info_label = tk.Label(
            activity_frame,
            text=info_text,
            justify=tk.LEFT,
            font=("Segoe UI", 10),
            wraplength=800,
            fg="#374151"
        )
        info_label.pack(anchor="w")

    def show_receptions(self):
        """Module Réceptions"""
        self.clear_content()
        self.update_status("Module Réceptions", "📦")
        self.create_module_placeholder("📦 Réceptions", "Gestion des réceptions de phosphate")

    def show_production(self):
        """Module Production"""
        self.clear_content()
        self.update_status("Module Production", "🏭")
        self.create_module_placeholder("🏭 Production", "Suivi de la production journalière")

    def show_arrets(self):
        """Module Arrêts"""
        self.clear_content()
        self.update_status("Module Arrêts", "⚠️")
        self.create_module_placeholder("⚠️ Arrêts", "Gestion des arrêts et maintenance")

    def show_ventes(self):
        """Module Ventes"""
        self.clear_content()
        self.update_status("Module Ventes", "💰")
        self.create_module_placeholder("💰 Ventes", "Planning et suivi des ventes")

    def show_statistics(self):
        """Module Statistiques"""
        self.clear_content()
        self.update_status("Module Statistiques", "📈")
        self.create_module_placeholder("📈 Statistiques", "Analyses et KPI globaux")

    def create_module_placeholder(self, title, description):
        """Créer un placeholder pour les modules"""
        title_label = tk.Label(
            self.content_frame,
            text=title,
            font=("Segoe UI", 16, "bold"),
            fg="#1e3a8a" if self.theme else "#333"
        )
        title_label.grid(row=0, column=0, pady=(0, 20))

        desc_label = tk.Label(
            self.content_frame,
            text=f"{description}\n\n✨ Module complet disponible dans la version développeur\n📋 Interface complète avec toutes les fonctionnalités",
            font=("Segoe UI", 11),
            fg="#6b7280",
            justify=tk.CENTER
        )
        desc_label.grid(row=1, column=0, pady=20)

        # Bouton d'action
        action_btn = tk.Button(
            self.content_frame,
            text="🚀 Accéder au module complet",
            command=lambda: messagebox.showinfo("Module", f"Module {title} disponible dans gui_main.py"),
            bg="#3b82f6" if self.theme else "#007acc",
            fg="white",
            font=("Segoe UI", 10, "bold"),
            relief="flat",
            padx=20,
            pady=10,
            cursor="hand2"
        )
        action_btn.grid(row=2, column=0, pady=20)

    def load_data(self):
        """Charger les données initiales"""
        try:
            if os.path.exists("sotramine_phosphate.db"):
                self.update_status("Base de données connectée", "✅")
            else:
                self.update_status("Base de données non trouvée", "⚠️")
        except Exception as e:
            self.update_status(f"Erreur: {e}", "❌")

    # Méthodes des menus
    def import_excel(self):
        """Importer des données Excel"""
        self.update_status("Import Excel", "📊")

        try:
            # Sélectionner le fichier Excel
            file_path = filedialog.askopenfilename(
                title="Sélectionner le fichier Excel",
                filetypes=[
                    ("Fichiers Excel", "*.xlsx *.xls"),
                    ("Tous les fichiers", "*.*")
                ]
            )

            if not file_path:
                return

            # Vérifier les dépendances
            try:
                import pandas as pd
                import openpyxl
            except ImportError as e:
                messagebox.showerror(
                    "Dépendances manquantes",
                    f"Module requis non installé: {e}\n\n"
                    "Installez avec: pip install pandas openpyxl"
                )
                return

            # Fenêtre de progression
            progress_window = tk.Toplevel(self.root)
            progress_window.title("Import en cours...")
            progress_window.geometry("400x150")
            progress_window.transient(self.root)
            progress_window.grab_set()

            # Centrer la fenêtre
            progress_window.update_idletasks()
            x = (progress_window.winfo_screenwidth() // 2) - 200
            y = (progress_window.winfo_screenheight() // 2) - 75
            progress_window.geometry(f"+{x}+{y}")

            # Contenu de la fenêtre de progression
            tk.Label(
                progress_window,
                text="📊 Import des données Excel en cours...",
                font=("Segoe UI", 12, "bold")
            ).pack(pady=20)

            progress_var = tk.StringVar(value="Lecture du fichier Excel...")
            progress_label = tk.Label(progress_window, textvariable=progress_var)
            progress_label.pack(pady=10)

            progress_window.update()

            # Lire le fichier Excel
            try:
                progress_var.set("📖 Lecture des feuilles Excel...")
                progress_window.update()

                # Lire toutes les feuilles
                excel_file = pd.ExcelFile(file_path)
                sheets = excel_file.sheet_names

                progress_var.set(f"📋 {len(sheets)} feuilles détectées...")
                progress_window.update()

                imported_data = {}
                total_records = 0

                for sheet_name in sheets:
                    progress_var.set(f"📊 Traitement: {sheet_name}...")
                    progress_window.update()

                    df = pd.read_excel(file_path, sheet_name=sheet_name)
                    imported_data[sheet_name] = df
                    total_records += len(df)

                progress_var.set("✅ Import terminé avec succès!")
                progress_window.update()

                # Fermer la fenêtre de progression après 1 seconde
                progress_window.after(1000, progress_window.destroy)

                # Afficher le résumé
                summary = f"""
📊 Import Excel Terminé

📁 Fichier: {os.path.basename(file_path)}
📋 Feuilles importées: {len(sheets)}
📊 Total enregistrements: {total_records:,}

Feuilles détectées:
"""
                for sheet_name, df in imported_data.items():
                    summary += f"• {sheet_name}: {len(df)} lignes\n"

                summary += f"""
💾 Les données sont prêtes pour traitement.
🔧 Utilisez gui_main.py pour l'import complet dans la base de données.
                """

                messagebox.showinfo("Import Réussi", summary)
                self.update_status(f"Import terminé: {total_records} enregistrements", "✅")

            except Exception as e:
                progress_window.destroy()
                messagebox.showerror(
                    "Erreur d'import",
                    f"Erreur lors de la lecture du fichier Excel:\n{e}\n\n"
                    "Vérifiez que le fichier n'est pas ouvert dans Excel."
                )

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'import: {e}")

    def export_data(self):
        """Exporter les données"""
        messagebox.showinfo("Export", "Fonctionnalité d'export des données")

    def show_config(self):
        """Afficher la configuration"""
        try:
            from config_manager import ConfigManager, ConfigGUI
            config_gui = ConfigGUI(self.root)
            config_gui.show_config_window()
        except ImportError:
            messagebox.showinfo("Configuration", "Interface de configuration\n\n⚙️ Module complet disponible dans config_manager.py")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ouverture de la configuration: {e}")

    def show_themes(self):
        """Afficher le sélecteur de thèmes"""
        try:
            from theme_selector import ThemeSelector
            theme_selector = ThemeSelector(self)
            theme_selector.show_selector()
        except ImportError:
            messagebox.showinfo("Thèmes", "Sélecteur de thèmes\n\n🎨 Module complet disponible dans theme_selector.py")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'ouverture du sélecteur: {e}")

    def refresh_theme(self):
        """Rafraîchir l'application avec le nouveau thème"""
        try:
            # Réinitialiser le système de thèmes
            if self.theme_manager:
                self.theme = self.theme_manager.get_theme()
                self.root.configure(bg=self.theme["colors"]["background"])

                # Mettre à jour les boutons de navigation
                for module_id, btn in self.nav_buttons.items():
                    self.style_nav_button(btn, module_id == self.current_module)

                self.update_status("Thème mis à jour", "✅")
                messagebox.showinfo("Thème appliqué", "Le nouveau thème a été appliqué avec succès!")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de l'application du thème: {e}")

    def run_diagnostic(self):
        """Lancer le diagnostic système"""
        self.update_status("Diagnostic en cours", "🔍")

        # Fenêtre de diagnostic
        diag_window = tk.Toplevel(self.root)
        diag_window.title("🔍 Diagnostic Système SOTRAMINE")
        diag_window.geometry("600x500")
        diag_window.transient(self.root)
        diag_window.grab_set()

        # Centrer la fenêtre
        diag_window.update_idletasks()
        x = (diag_window.winfo_screenwidth() // 2) - 300
        y = (diag_window.winfo_screenheight() // 2) - 250
        diag_window.geometry(f"+{x}+{y}")

        # Titre
        title_label = tk.Label(
            diag_window,
            text="🔍 Diagnostic Système SOTRAMINE",
            font=("Segoe UI", 14, "bold")
        )
        title_label.pack(pady=10)

        # Zone de texte pour les résultats
        text_frame = tk.Frame(diag_window)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        result_text = tk.Text(
            text_frame,
            wrap=tk.WORD,
            font=("Consolas", 10),
            bg="#f8f9fa",
            fg="#212529"
        )

        scrollbar = tk.Scrollbar(text_frame, orient=tk.VERTICAL, command=result_text.yview)
        result_text.configure(yscrollcommand=scrollbar.set)

        result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Bouton fermer
        close_btn = tk.Button(
            diag_window,
            text="❌ Fermer",
            command=diag_window.destroy,
            font=("Segoe UI", 10)
        )
        close_btn.pack(pady=10)

        # Exécuter le diagnostic
        def run_checks():
            result_text.insert(tk.END, "🔍 DIAGNOSTIC SYSTÈME SOTRAMINE\n")
            result_text.insert(tk.END, "=" * 50 + "\n\n")

            # 1. Vérifier Python
            result_text.insert(tk.END, "🐍 Python:\n")
            try:
                python_version = sys.version
                result_text.insert(tk.END, f"   ✅ Version: {python_version.split()[0]}\n")
                result_text.insert(tk.END, f"   📁 Chemin: {sys.executable}\n")
            except Exception as e:
                result_text.insert(tk.END, f"   ❌ Erreur: {e}\n")
            result_text.insert(tk.END, "\n")

            # 2. Vérifier les dépendances
            result_text.insert(tk.END, "📦 Dépendances:\n")
            dependencies = [
                ("tkinter", "Interface graphique"),
                ("sqlite3", "Base de données"),
                ("pandas", "Traitement données"),
                ("openpyxl", "Fichiers Excel"),
                ("datetime", "Gestion dates"),
                ("os", "Système de fichiers")
            ]

            for module_name, description in dependencies:
                try:
                    __import__(module_name)
                    result_text.insert(tk.END, f"   ✅ {module_name}: {description}\n")
                except ImportError:
                    result_text.insert(tk.END, f"   ❌ {module_name}: MANQUANT - {description}\n")
            result_text.insert(tk.END, "\n")

            # 3. Vérifier les fichiers
            result_text.insert(tk.END, "📁 Fichiers système:\n")
            required_files = [
                ("sotramine_final.py", "Application principale"),
                ("database_models.py", "Modèles base de données"),
                ("themes.py", "Système de thèmes"),
                ("config.py", "Configuration"),
                ("sotramine_phosphate.db", "Base de données")
            ]

            for filename, description in required_files:
                if os.path.exists(filename):
                    size = os.path.getsize(filename)
                    result_text.insert(tk.END, f"   ✅ {filename}: {description} ({size:,} bytes)\n")
                else:
                    result_text.insert(tk.END, f"   ⚠️ {filename}: MANQUANT - {description}\n")
            result_text.insert(tk.END, "\n")

            # 4. Vérifier la base de données
            result_text.insert(tk.END, "💾 Base de données:\n")
            try:
                if os.path.exists("sotramine_phosphate.db"):
                    import sqlite3
                    conn = sqlite3.connect("sotramine_phosphate.db")
                    cursor = conn.cursor()

                    # Lister les tables
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()

                    result_text.insert(tk.END, f"   ✅ Connexion réussie\n")
                    result_text.insert(tk.END, f"   📋 Tables: {len(tables)}\n")

                    for table in tables:
                        cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                        count = cursor.fetchone()[0]
                        result_text.insert(tk.END, f"      • {table[0]}: {count:,} enregistrements\n")

                    conn.close()
                else:
                    result_text.insert(tk.END, "   ⚠️ Base de données non trouvée\n")
            except Exception as e:
                result_text.insert(tk.END, f"   ❌ Erreur: {e}\n")
            result_text.insert(tk.END, "\n")

            # 5. Vérifier l'interface
            result_text.insert(tk.END, "🖥️ Interface graphique:\n")
            try:
                screen_width = diag_window.winfo_screenwidth()
                screen_height = diag_window.winfo_screenheight()
                result_text.insert(tk.END, f"   ✅ Résolution: {screen_width}x{screen_height}\n")

                if screen_width >= 1200 and screen_height >= 700:
                    result_text.insert(tk.END, "   ✅ Résolution compatible\n")
                else:
                    result_text.insert(tk.END, "   ⚠️ Résolution faible (min 1200x700 recommandé)\n")

                result_text.insert(tk.END, f"   ✅ Tkinter fonctionnel\n")
            except Exception as e:
                result_text.insert(tk.END, f"   ❌ Erreur: {e}\n")
            result_text.insert(tk.END, "\n")

            # 6. Résumé
            result_text.insert(tk.END, "📋 RÉSUMÉ:\n")
            result_text.insert(tk.END, "=" * 20 + "\n")
            result_text.insert(tk.END, "✅ Diagnostic terminé\n")
            result_text.insert(tk.END, f"📅 Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
            result_text.insert(tk.END, "🚀 Application prête pour utilisation\n\n")

            result_text.insert(tk.END, "💡 Conseils:\n")
            result_text.insert(tk.END, "• Sauvegardez régulièrement vos données\n")
            result_text.insert(tk.END, "• Maintenez Python et les dépendances à jour\n")
            result_text.insert(tk.END, "• Consultez la documentation en cas de problème\n")

            # Aller au début
            result_text.see(tk.INSERT)

        # Lancer le diagnostic après un court délai
        diag_window.after(100, run_checks)

        self.update_status("Diagnostic terminé", "✅")

    def backup_database(self):
        """Sauvegarder la base de données"""
        try:
            if os.path.exists("sotramine_phosphate.db"):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_sotramine_{timestamp}.db"

                import shutil
                shutil.copy2("sotramine_phosphate.db", backup_name)

                messagebox.showinfo("Sauvegarde", f"Base de données sauvegardée:\n{backup_name}")
                self.update_status("Sauvegarde créée", "💾")
            else:
                messagebox.showwarning("Sauvegarde", "Aucune base de données à sauvegarder")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def show_help(self):
        """Afficher l'aide"""
        help_text = """
🏭 AIDE SOTRAMINE - Version Finale

📋 Navigation:
• Utilisez le menu de gauche pour naviguer entre les modules
• Le tableau de bord affiche un résumé des données importantes
• Chaque module gère un aspect spécifique du suivi phosphate

🔧 Modules disponibles:
📦 Réceptions - Gestion des arrivages de phosphate
🏭 Production - Suivi de la production journalière
⚠️ Arrêts - Gestion des arrêts et maintenance
💰 Ventes - Planning et suivi des ventes clients
📈 Statistiques - Analyses globales et KPI

⚙️ Outils:
📊 Import Excel - Importer vos données existantes
🎨 Thèmes - Personnaliser l'apparence
🔍 Diagnostic - Vérifier le système
💾 Sauvegarde - Protéger vos données

📖 Pour plus d'aide, consultez GUIDE_UTILISATION.md
        """
        messagebox.showinfo("Aide SOTRAMINE", help_text)

    def show_about(self):
        """Afficher les informations"""
        app_info = get_app_info()
        about_text = f"""
🏭 {app_info['name']}
Version: {app_info['version']} - Finale

📝 Description:
Système professionnel de suivi et gestion
des opérations phosphate

🎯 Fonctionnalités:
• Interface graphique moderne
• 6 modules de gestion complets
• Système de thèmes personnalisables
• Import/Export de données
• Statistiques et analyses avancées

👨‍💻 Développé avec:
• Python 3.x
• Tkinter (Interface graphique)
• SQLite (Base de données)
• Architecture modulaire

📅 Date: {datetime.now().strftime('%d/%m/%Y')}

🎉 Application prête pour la production !
        """
        messagebox.showinfo("À propos", about_text)

    def quit_app(self):
        """Quitter l'application"""
        if messagebox.askyesno("Quitter", "Voulez-vous vraiment quitter SOTRAMINE ?"):
            self.root.quit()

    def run(self):
        """Lancer l'application"""
        self.root.mainloop()

def check_and_install_dependencies():
    """Vérifier et installer les dépendances"""
    missing = []

    try:
        import pandas
    except ImportError:
        missing.append("pandas")

    try:
        import openpyxl
    except ImportError:
        missing.append("openpyxl")

    if missing:
        print(f"📦 Installation des dépendances: {', '.join(missing)}")
        try:
            import subprocess
            for dep in missing:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print("✅ Dépendances installées avec succès")
            return True
        except Exception as e:
            print(f"❌ Erreur d'installation: {e}")
            return False
    return True

def initialize_database():
    """Initialiser la base de données si nécessaire"""
    if not os.path.exists("sotramine_phosphate.db"):
        print("💾 Création de la base de données...")
        try:
            from database_models import DatabaseManager
            db = DatabaseManager()
            print("✅ Base de données créée")
            return True
        except Exception as e:
            print(f"❌ Erreur base de données: {e}")
            return False
    return True

def main():
    """Fonction principale"""
    print("🏭 SOTRAMINE - Version Finale")
    print("=" * 50)

    # Vérifier les dépendances
    if not check_and_install_dependencies():
        input("❌ Erreur dépendances. Appuyez sur Entrée...")
        return

    # Initialiser la base de données
    if not initialize_database():
        input("❌ Erreur base de données. Appuyez sur Entrée...")
        return

    # Lancer l'application
    print("🚀 Lancement de l'interface...")
    try:
        app = SotramineApp()
        print("✅ Application chargée avec succès")
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
