#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🏭 SOTRAMINE - Version Finale
Système de Suivi Phosphate - Interface Professionnelle Complète
Version: 1.0 Final
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import sys
from datetime import datetime
import sqlite3

# Imports des modules SOTRAMINE
try:
    from database_models import DatabaseManager
    from data_managers import (
        ReceptionPhosphateManager, ProductionManager, ArretManager,
        VenteManager, BilanManager
    )
    from config import get_config, get_app_info
    from themes import ThemeManager, ModernStyles, initialize_theme_system
except ImportError as e:
    print(f"⚠️ Modules manquants: {e}")

class SotramineApp:
    """Application SOTRAMINE - Version Finale"""

    def __init__(self):
        self.root = tk.Tk()
        self.setup_application()
        self.create_interface()
        self.load_data()

    def setup_application(self):
        """Configuration initiale de l'application"""
        # Configuration de base
        app_info = get_app_info()
        self.root.title(f"🏭 {app_info['name']} v{app_info['version']} - Version Finale")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)

        # Initialiser le système de thèmes
        try:
            initialize_theme_system(self.root)
            self.theme_manager = ThemeManager()
            self.theme = self.theme_manager.get_theme()
            self.root.configure(bg=self.theme["colors"]["background"])
        except:
            self.theme_manager = None
            self.theme = None
            self.root.configure(bg='#f0f0f0')

        # Variables d'état
        self.status_var = tk.StringVar(value="✅ Application initialisée")
        self.current_module = "dashboard"

        # Centrer la fenêtre
        self.center_window()

        # Configuration du grid principal
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)

        # Créer le menu
        self.create_menu()

    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - 700
        y = (self.root.winfo_screenheight() // 2) - 450
        self.root.geometry(f"+{x}+{y}")

    def create_menu(self):
        """Créer la barre de menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Menu Fichier
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="📁 Fichier", menu=file_menu)
        file_menu.add_command(label="📊 Importer Excel", command=self.import_excel)
        file_menu.add_command(label="📤 Exporter données", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="❌ Quitter", command=self.quit_app)

        # Menu Outils
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="🔧 Outils", menu=tools_menu)
        tools_menu.add_command(label="⚙️ Configuration", command=self.show_config)
        tools_menu.add_command(label="🎨 Thèmes", command=self.show_themes)
        tools_menu.add_command(label="🔍 Diagnostic", command=self.run_diagnostic)
        tools_menu.add_separator()
        tools_menu.add_command(label="💾 Sauvegarde", command=self.backup_database)

        # Menu Aide
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="❓ Aide", menu=help_menu)
        help_menu.add_command(label="📖 Guide", command=self.show_help)
        help_menu.add_command(label="ℹ️ À propos", command=self.show_about)

    def create_interface(self):
        """Créer l'interface principale"""
        # Frame principal
        self.main_frame = tk.Frame(self.root)
        self.main_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        self.main_frame.columnconfigure(1, weight=1)
        self.main_frame.rowconfigure(1, weight=1)

        # En-tête
        self.create_header()

        # Navigation et contenu
        self.create_navigation()
        self.create_content_area()

        # Barre de statut
        self.create_status_bar()

    def create_header(self):
        """Créer l'en-tête professionnel"""
        if self.theme:
            bg_color = self.theme["colors"]["primary"]
            fg_color = "white"
        else:
            bg_color = "#1e3a8a"
            fg_color = "white"

        header_frame = tk.Frame(self.main_frame, bg=bg_color, height=80)
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 20))
        header_frame.grid_propagate(False)

        # Logo et titre
        title_label = tk.Label(
            header_frame,
            text="🏭 SOTRAMINE",
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 20, "bold")
        )
        title_label.place(x=20, y=15)

        # Sous-titre
        subtitle_label = tk.Label(
            header_frame,
            text="Système de Suivi Phosphate - Version Finale",
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 11)
        )
        subtitle_label.place(x=250, y=25)

        # Informations système
        app_info = get_app_info()
        info_label = tk.Label(
            header_frame,
            text=f"v{app_info['version']} | {datetime.now().strftime('%d/%m/%Y')}",
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 9)
        )
        info_label.place(relx=1.0, x=-20, y=25, anchor="ne")

    def create_navigation(self):
        """Créer la navigation moderne"""
        nav_frame = ttk.LabelFrame(
            self.main_frame,
            text="📋 Navigation",
            padding="15"
        )
        nav_frame.grid(row=1, column=0, sticky="ns", padx=(0, 15))

        # Modules principaux
        modules = [
            ("📊", "Tableau de bord", "dashboard", self.show_dashboard),
            ("📦", "Réceptions", "receptions", self.show_receptions),
            ("🏭", "Production", "production", self.show_production),
            ("⚠️", "Arrêts", "arrets", self.show_arrets),
            ("💰", "Ventes", "ventes", self.show_ventes),
            ("📈", "Statistiques", "stats", self.show_statistics),
            ("📊", "Import Excel", "import", self.import_excel),
        ]

        self.nav_buttons = {}
        for i, (icon, text, module_id, command) in enumerate(modules):
            btn = tk.Button(
                nav_frame,
                text=f"{icon} {text}",
                command=lambda cmd=command, mid=module_id: self.switch_module(cmd, mid),
                width=18,
                anchor="w",
                relief="flat",
                padx=12,
                pady=10,
                cursor="hand2",
                font=("Segoe UI", 10)
            )
            btn.grid(row=i, column=0, pady=2, sticky="ew")
            self.nav_buttons[module_id] = btn

            # Style du bouton
            self.style_nav_button(btn, module_id == "dashboard")

    def style_nav_button(self, button, is_active=False):
        """Appliquer le style aux boutons de navigation"""
        if self.theme:
            if is_active:
                button.configure(
                    bg=self.theme["colors"]["primary"],
                    fg="white",
                    activebackground=self.theme["colors"]["secondary"]
                )
            else:
                button.configure(
                    bg=self.theme["colors"]["surface"],
                    fg=self.theme["colors"]["text_primary"],
                    activebackground=self.theme["colors"]["secondary"]
                )
        else:
            if is_active:
                button.configure(bg="#1e3a8a", fg="white")
            else:
                button.configure(bg="#f1f5f9", fg="#1e293b")

    def switch_module(self, command, module_id):
        """Changer de module"""
        # Mettre à jour les styles des boutons
        for mid, btn in self.nav_buttons.items():
            self.style_nav_button(btn, mid == module_id)

        # Exécuter la commande
        self.current_module = module_id
        command()

    def create_content_area(self):
        """Créer la zone de contenu"""
        self.content_frame = ttk.LabelFrame(
            self.main_frame,
            text="📄 Contenu Principal",
            padding="20"
        )
        self.content_frame.grid(row=1, column=1, sticky="nsew")
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)

        # Afficher le tableau de bord par défaut
        self.show_dashboard()

    def create_status_bar(self):
        """Créer la barre de statut"""
        if self.theme:
            bg_color = self.theme["colors"]["surface"]
            fg_color = self.theme["colors"]["text_primary"]
        else:
            bg_color = "#e5e7eb"
            fg_color = "#374151"

        status_frame = tk.Frame(
            self.main_frame,
            bg=bg_color,
            height=30,
            relief="solid",
            borderwidth=1
        )
        status_frame.grid(row=2, column=0, columnspan=2, sticky="ew", pady=(20, 0))
        status_frame.grid_propagate(False)

        # Statut
        status_label = tk.Label(
            status_frame,
            textvariable=self.status_var,
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 9)
        )
        status_label.place(x=15, y=6)

        # Horloge
        self.time_var = tk.StringVar()
        time_label = tk.Label(
            status_frame,
            textvariable=self.time_var,
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 9)
        )
        time_label.place(relx=1.0, x=-15, y=6, anchor="ne")

        # Informations système
        try:
            if os.path.exists("sotramine_phosphate.db"):
                db_status = "💾 BD Connectée"
            else:
                db_status = "⚠️ BD Manquante"
        except:
            db_status = "❌ BD Erreur"

        db_label = tk.Label(
            status_frame,
            text=db_status,
            bg=bg_color,
            fg=fg_color,
            font=("Segoe UI", 9)
        )
        db_label.place(relx=0.5, anchor="n", y=6)

        # Démarrer l'horloge
        self.update_clock()

    def update_clock(self):
        """Mettre à jour l'horloge"""
        self.time_var.set(datetime.now().strftime("%H:%M:%S"))
        self.root.after(1000, self.update_clock)

    def clear_content(self):
        """Vider la zone de contenu"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def update_status(self, message, icon="ℹ️"):
        """Mettre à jour le statut"""
        self.status_var.set(f"{icon} {message}")

    def show_dashboard(self):
        """Afficher le tableau de bord"""
        self.clear_content()
        self.update_status("Tableau de bord chargé", "📊")

        # Titre
        title = tk.Label(
            self.content_frame,
            text="📊 Tableau de Bord Exécutif",
            font=("Segoe UI", 18, "bold"),
            fg="#1e3a8a" if self.theme else "#333"
        )
        title.grid(row=0, column=0, pady=(0, 30))

        # Statistiques rapides
        self.create_dashboard_stats()

        # Activité récente
        self.create_recent_activity()

    def create_dashboard_stats(self):
        """Créer les statistiques du tableau de bord"""
        stats_frame = tk.Frame(self.content_frame)
        stats_frame.grid(row=1, column=0, sticky="ew", pady=(0, 30))

        try:
            # Obtenir les données
            receptions = ReceptionPhosphateManager.obtenir_receptions()
            productions = ProductionManager.obtenir_production()
            ventes = VenteManager.obtenir_ventes()

            # Calculer les statistiques
            stats = [
                ("📦", "Réceptions", len(receptions), "#3b82f6"),
                ("🏭", "Productions", len(productions), "#10b981"),
                ("💰", "Ventes", len(ventes), "#f59e0b"),
                ("⚖️", "Tonnage Total", f"{sum(r.get('tonnage', 0) for r in receptions):,.0f} T", "#ef4444")
            ]

            for i, (icon, title, value, color) in enumerate(stats):
                self.create_stat_card(stats_frame, icon, title, str(value), color, i)

        except Exception as e:
            error_label = tk.Label(
                stats_frame,
                text=f"⚠️ Erreur lors du chargement des statistiques: {e}",
                font=("Segoe UI", 10),
                fg="#ef4444"
            )
            error_label.grid(row=0, column=0, pady=20)

    def create_stat_card(self, parent, icon, title, value, color, position):
        """Créer une carte de statistique"""
        card = tk.Frame(
            parent,
            bg="white",
            relief="solid",
            borderwidth=1,
            padx=20,
            pady=15
        )
        card.grid(row=position//2, column=position%2, padx=10, pady=10, sticky="ew")

        # En-tête coloré
        header = tk.Frame(card, bg=color, height=4)
        header.pack(fill=tk.X, pady=(0, 10))

        # Icône et titre
        title_frame = tk.Frame(card, bg="white")
        title_frame.pack(fill=tk.X)

        icon_label = tk.Label(
            title_frame,
            text=icon,
            bg="white",
            font=("Segoe UI", 20),
            fg=color
        )
        icon_label.pack(side=tk.LEFT)

        title_label = tk.Label(
            title_frame,
            text=title,
            bg="white",
            fg="#374151",
            font=("Segoe UI", 11, "bold")
        )
        title_label.pack(side=tk.LEFT, padx=(10, 0))

        # Valeur
        value_label = tk.Label(
            card,
            text=value,
            bg="white",
            fg=color,
            font=("Segoe UI", 24, "bold")
        )
        value_label.pack(pady=(5, 0))

        parent.columnconfigure(position%2, weight=1)

    def create_recent_activity(self):
        """Créer la section d'activité récente"""
        activity_frame = ttk.LabelFrame(
            self.content_frame,
            text="📈 Activité Récente",
            padding="15"
        )
        activity_frame.grid(row=2, column=0, sticky="ew", pady=(0, 20))

        # Message d'information
        info_text = """
🎉 Bienvenue dans SOTRAMINE - Version Finale

✨ Fonctionnalités disponibles :
• 📦 Gestion complète des réceptions de phosphate
• 🏭 Suivi détaillé de la production journalière
• ⚠️ Gestion des arrêts et maintenance (laverie/concentrateur)
• 💰 Planning et suivi complet des ventes
• 📈 Statistiques et analyses avancées avec KPI
• 🎨 Système de thèmes professionnels personnalisables

🚀 Utilisez la navigation de gauche pour accéder aux modules.
📊 Importez vos données Excel via le menu Fichier.
⚙️ Personnalisez l'interface via le menu Outils.
        """

        info_label = tk.Label(
            activity_frame,
            text=info_text,
            justify=tk.LEFT,
            font=("Segoe UI", 10),
            wraplength=800,
            fg="#374151"
        )
        info_label.pack(anchor="w")

    def show_receptions(self):
        """Module Réceptions"""
        self.clear_content()
        self.update_status("Module Réceptions", "📦")
        self.create_module_placeholder("📦 Réceptions", "Gestion des réceptions de phosphate")

    def show_production(self):
        """Module Production"""
        self.clear_content()
        self.update_status("Module Production", "🏭")
        self.create_module_placeholder("🏭 Production", "Suivi de la production journalière")

    def show_arrets(self):
        """Module Arrêts"""
        self.clear_content()
        self.update_status("Module Arrêts", "⚠️")
        self.create_module_placeholder("⚠️ Arrêts", "Gestion des arrêts et maintenance")

    def show_ventes(self):
        """Module Ventes"""
        self.clear_content()
        self.update_status("Module Ventes", "💰")
        self.create_module_placeholder("💰 Ventes", "Planning et suivi des ventes")

    def show_statistics(self):
        """Module Statistiques"""
        self.clear_content()
        self.update_status("Module Statistiques", "📈")
        self.create_module_placeholder("📈 Statistiques", "Analyses et KPI globaux")

    def create_module_placeholder(self, title, description):
        """Créer un placeholder pour les modules"""
        title_label = tk.Label(
            self.content_frame,
            text=title,
            font=("Segoe UI", 16, "bold"),
            fg="#1e3a8a" if self.theme else "#333"
        )
        title_label.grid(row=0, column=0, pady=(0, 20))

        desc_label = tk.Label(
            self.content_frame,
            text=f"{description}\n\n✨ Module complet disponible dans la version développeur\n📋 Interface complète avec toutes les fonctionnalités",
            font=("Segoe UI", 11),
            fg="#6b7280",
            justify=tk.CENTER
        )
        desc_label.grid(row=1, column=0, pady=20)

        # Bouton d'action
        action_btn = tk.Button(
            self.content_frame,
            text="🚀 Accéder au module complet",
            command=lambda: messagebox.showinfo("Module", f"Module {title} disponible dans gui_main.py"),
            bg="#3b82f6" if self.theme else "#007acc",
            fg="white",
            font=("Segoe UI", 10, "bold"),
            relief="flat",
            padx=20,
            pady=10,
            cursor="hand2"
        )
        action_btn.grid(row=2, column=0, pady=20)

    def load_data(self):
        """Charger les données initiales"""
        try:
            if os.path.exists("sotramine_phosphate.db"):
                self.update_status("Base de données connectée", "✅")
            else:
                self.update_status("Base de données non trouvée", "⚠️")
        except Exception as e:
            self.update_status(f"Erreur: {e}", "❌")

    # Méthodes des menus
    def import_excel(self):
        """Importer des données Excel"""
        self.update_status("Import Excel", "📊")
        messagebox.showinfo("Import Excel", "Fonctionnalité d'import Excel\n\n📋 Module complet disponible dans gui_main.py")

    def export_data(self):
        """Exporter les données"""
        messagebox.showinfo("Export", "Fonctionnalité d'export des données")

    def show_config(self):
        """Afficher la configuration"""
        messagebox.showinfo("Configuration", "Interface de configuration\n\n⚙️ Module complet disponible dans config_manager.py")

    def show_themes(self):
        """Afficher le sélecteur de thèmes"""
        messagebox.showinfo("Thèmes", "Sélecteur de thèmes\n\n🎨 Module complet disponible dans theme_selector.py")

    def run_diagnostic(self):
        """Lancer le diagnostic"""
        messagebox.showinfo("Diagnostic", "Outil de diagnostic système\n\n🔍 Module complet disponible dans diagnostic.py")

    def backup_database(self):
        """Sauvegarder la base de données"""
        try:
            if os.path.exists("sotramine_phosphate.db"):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"backup_sotramine_{timestamp}.db"

                import shutil
                shutil.copy2("sotramine_phosphate.db", backup_name)

                messagebox.showinfo("Sauvegarde", f"Base de données sauvegardée:\n{backup_name}")
                self.update_status("Sauvegarde créée", "💾")
            else:
                messagebox.showwarning("Sauvegarde", "Aucune base de données à sauvegarder")
        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la sauvegarde: {e}")

    def show_help(self):
        """Afficher l'aide"""
        help_text = """
🏭 AIDE SOTRAMINE - Version Finale

📋 Navigation:
• Utilisez le menu de gauche pour naviguer entre les modules
• Le tableau de bord affiche un résumé des données importantes
• Chaque module gère un aspect spécifique du suivi phosphate

🔧 Modules disponibles:
📦 Réceptions - Gestion des arrivages de phosphate
🏭 Production - Suivi de la production journalière
⚠️ Arrêts - Gestion des arrêts et maintenance
💰 Ventes - Planning et suivi des ventes clients
📈 Statistiques - Analyses globales et KPI

⚙️ Outils:
📊 Import Excel - Importer vos données existantes
🎨 Thèmes - Personnaliser l'apparence
🔍 Diagnostic - Vérifier le système
💾 Sauvegarde - Protéger vos données

📖 Pour plus d'aide, consultez GUIDE_UTILISATION.md
        """
        messagebox.showinfo("Aide SOTRAMINE", help_text)

    def show_about(self):
        """Afficher les informations"""
        app_info = get_app_info()
        about_text = f"""
🏭 {app_info['name']}
Version: {app_info['version']} - Finale

📝 Description:
Système professionnel de suivi et gestion
des opérations phosphate

🎯 Fonctionnalités:
• Interface graphique moderne
• 6 modules de gestion complets
• Système de thèmes personnalisables
• Import/Export de données
• Statistiques et analyses avancées

👨‍💻 Développé avec:
• Python 3.x
• Tkinter (Interface graphique)
• SQLite (Base de données)
• Architecture modulaire

📅 Date: {datetime.now().strftime('%d/%m/%Y')}

🎉 Application prête pour la production !
        """
        messagebox.showinfo("À propos", about_text)

    def quit_app(self):
        """Quitter l'application"""
        if messagebox.askyesno("Quitter", "Voulez-vous vraiment quitter SOTRAMINE ?"):
            self.root.quit()

    def run(self):
        """Lancer l'application"""
        self.root.mainloop()

def check_and_install_dependencies():
    """Vérifier et installer les dépendances"""
    missing = []

    try:
        import pandas
    except ImportError:
        missing.append("pandas")

    try:
        import openpyxl
    except ImportError:
        missing.append("openpyxl")

    if missing:
        print(f"📦 Installation des dépendances: {', '.join(missing)}")
        try:
            import subprocess
            for dep in missing:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print("✅ Dépendances installées avec succès")
            return True
        except Exception as e:
            print(f"❌ Erreur d'installation: {e}")
            return False
    return True

def initialize_database():
    """Initialiser la base de données si nécessaire"""
    if not os.path.exists("sotramine_phosphate.db"):
        print("💾 Création de la base de données...")
        try:
            from database_models import DatabaseManager
            db = DatabaseManager()
            print("✅ Base de données créée")
            return True
        except Exception as e:
            print(f"❌ Erreur base de données: {e}")
            return False
    return True

def main():
    """Fonction principale"""
    print("🏭 SOTRAMINE - Version Finale")
    print("=" * 50)

    # Vérifier les dépendances
    if not check_and_install_dependencies():
        input("❌ Erreur dépendances. Appuyez sur Entrée...")
        return

    # Initialiser la base de données
    if not initialize_database():
        input("❌ Erreur base de données. Appuyez sur Entrée...")
        return

    # Lancer l'application
    print("🚀 Lancement de l'interface...")
    try:
        app = SotramineApp()
        print("✅ Application chargée avec succès")
        app.run()
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        input("Appuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    main()
