#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test du nouveau design professionnel SOTRAMINE
Version simplifiée pour tester l'interface
"""

import tkinter as tk
from tkinter import ttk
from themes import ThemeManager, ModernStyles, initialize_theme_system

class TestDesignApp:
    """Application de test pour le nouveau design"""
    
    def __init__(self):
        self.root = tk.Tk()
        
        # Initialiser le système de thèmes
        initialize_theme_system(self.root)
        self.theme_manager = ThemeManager()
        self.modern_styles = ModernStyles(self.theme_manager)
        self.modern_styles.configure_styles()
        
        self.setup_window()
        self.create_interface()
    
    def setup_window(self):
        """Configuration de la fenêtre"""
        self.root.title("🏭 SOTRAMINE - Test Design Professionnel")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)
        
        # Appliquer le thème
        theme = self.theme_manager.get_theme()
        self.root.configure(bg=theme["colors"]["background"])
        
        # Centrer la fenêtre
        self.center_window()
    
    def center_window(self):
        """Centrer la fenêtre sur l'écran"""
        self.root.update_idletasks()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = self.root.winfo_reqwidth()
        window_height = self.root.winfo_reqheight()
        x = (screen_width // 2) - (window_width // 2)
        y = (screen_height // 2) - (window_height // 2)
        self.root.geometry(f"+{x}+{y}")
    
    def create_interface(self):
        """Créer l'interface de test"""
        theme = self.theme_manager.get_theme()
        
        # Frame principal
        main_frame = ttk.Frame(self.root, style="Modern.TFrame", padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # En-tête moderne
        self.create_header(main_frame)
        
        # Corps de l'application
        body_frame = ttk.Frame(main_frame, style="Modern.TFrame")
        body_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        # Navigation (gauche)
        self.create_navigation(body_frame)
        
        # Contenu (droite)
        self.create_content(body_frame)
        
        # Barre de statut
        self.create_status_bar()
    
    def create_header(self, parent):
        """Créer l'en-tête moderne"""
        theme = self.theme_manager.get_theme()
        
        header_frame = tk.Frame(
            parent,
            bg=theme["colors"]["primary"],
            height=80
        )
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # Titre
        title_label = tk.Label(
            header_frame,
            text="🏭 SOTRAMINE",
            bg=theme["colors"]["primary"],
            fg="white",
            font=theme["fonts"]["title"]
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # Sous-titre
        subtitle_label = tk.Label(
            header_frame,
            text="Système de Suivi Phosphate - Interface Professionnelle",
            bg=theme["colors"]["primary"],
            fg="white",
            font=theme["fonts"]["body"]
        )
        subtitle_label.pack(side=tk.LEFT, padx=(0, 20), pady=20)
        
        # Version
        version_label = tk.Label(
            header_frame,
            text="v1.0 - Design Test",
            bg=theme["colors"]["primary"],
            fg="white",
            font=theme["fonts"]["small"]
        )
        version_label.pack(side=tk.RIGHT, padx=20, pady=20)
    
    def create_navigation(self, parent):
        """Créer la navigation moderne"""
        theme = self.theme_manager.get_theme()
        
        nav_frame = ttk.LabelFrame(
            parent,
            text="📋 Navigation",
            style="Modern.TLabelframe",
            padding="15"
        )
        nav_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        
        # Boutons de navigation
        nav_buttons = [
            ("📊", "Tableau de bord", "primary"),
            ("📦", "Réceptions", "secondary"),
            ("🏭", "Production", "secondary"),
            ("⚠️", "Arrêts", "secondary"),
            ("💰", "Ventes", "secondary"),
            ("📈", "Statistiques", "secondary"),
            ("📊", "Import Excel", "accent"),
        ]
        
        for icon, text, style_type in nav_buttons:
            self.create_nav_button(nav_frame, icon, text, style_type)
    
    def create_nav_button(self, parent, icon, text, style_type):
        """Créer un bouton de navigation"""
        theme = self.theme_manager.get_theme()
        
        if style_type == "primary":
            bg_color = theme["colors"]["primary"]
            fg_color = "white"
        elif style_type == "accent":
            bg_color = theme["colors"]["accent"]
            fg_color = "white"
        else:
            bg_color = theme["colors"]["surface"]
            fg_color = theme["colors"]["text_primary"]
        
        btn = tk.Button(
            parent,
            text=f"{icon} {text}",
            bg=bg_color,
            fg=fg_color,
            font=theme["fonts"]["body"],
            relief="flat",
            borderwidth=0,
            padx=15,
            pady=10,
            anchor="w",
            cursor="hand2",
            width=20
        )
        btn.pack(fill=tk.X, pady=3)
        
        # Effet hover
        def on_enter(event):
            btn.configure(bg=theme["colors"]["secondary"])
        
        def on_leave(event):
            btn.configure(bg=bg_color)
        
        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
    
    def create_content(self, parent):
        """Créer la zone de contenu"""
        content_frame = ttk.LabelFrame(
            parent,
            text="📄 Contenu Principal",
            style="Modern.TLabelframe",
            padding="20"
        )
        content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Titre du contenu
        title = ttk.Label(
            content_frame,
            text="📊 Tableau de Bord Exécutif",
            style='Title.TLabel'
        )
        title.pack(pady=(0, 30))
        
        # Cartes de statistiques
        self.create_stat_cards(content_frame)
        
        # Sections inférieures
        self.create_bottom_sections(content_frame)
    
    def create_stat_cards(self, parent):
        """Créer les cartes de statistiques"""
        theme = self.theme_manager.get_theme()
        
        cards_frame = tk.Frame(parent, bg=theme["colors"]["background"])
        cards_frame.pack(fill=tk.X, pady=(0, 30))
        
        # Données de test
        stats = [
            {"title": "Réceptions", "value": "152", "subtitle": "Total enregistrements", "icon": "📦", "color": theme["colors"]["primary"]},
            {"title": "Productions", "value": "100", "subtitle": "Cycles de production", "icon": "🏭", "color": theme["colors"]["secondary"]},
            {"title": "Ventes", "value": "1", "subtitle": "Commandes clients", "icon": "💰", "color": theme["colors"]["success"]},
            {"title": "Tonnage Total", "value": "45,230", "subtitle": "Tonnes reçues", "icon": "⚖️", "color": theme["colors"]["accent"]}
        ]
        
        for i, stat in enumerate(stats):
            self.create_professional_card(cards_frame, stat, i)
    
    def create_professional_card(self, parent, stat_data, position):
        """Créer une carte professionnelle"""
        theme = self.theme_manager.get_theme()
        
        card_frame = tk.Frame(
            parent,
            bg="white",
            relief="solid",
            borderwidth=1,
            padx=20,
            pady=15
        )
        card_frame.grid(row=position//2, column=position%2, padx=10, pady=10, sticky="ew")
        
        # En-tête coloré
        header_frame = tk.Frame(card_frame, bg=stat_data["color"], height=5)
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Titre avec icône
        title_frame = tk.Frame(card_frame, bg="white")
        title_frame.pack(fill=tk.X)
        
        icon_label = tk.Label(
            title_frame,
            text=stat_data["icon"],
            bg="white",
            font=("Segoe UI", 24),
            fg=stat_data["color"]
        )
        icon_label.pack(side=tk.LEFT)
        
        title_label = tk.Label(
            title_frame,
            text=stat_data["title"],
            bg="white",
            fg=theme["colors"]["text_primary"],
            font=theme["fonts"]["subheading"]
        )
        title_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Valeur
        value_label = tk.Label(
            card_frame,
            text=stat_data["value"],
            bg="white",
            fg=stat_data["color"],
            font=("Segoe UI", 28, "bold")
        )
        value_label.pack(pady=(10, 5))
        
        # Sous-titre
        subtitle_label = tk.Label(
            card_frame,
            text=stat_data["subtitle"],
            bg="white",
            fg=theme["colors"]["text_secondary"],
            font=theme["fonts"]["small"]
        )
        subtitle_label.pack()
        
        # Configuration de la grille
        parent.columnconfigure(position%2, weight=1)
    
    def create_bottom_sections(self, parent):
        """Créer les sections inférieures"""
        bottom_frame = tk.Frame(parent, bg=self.theme_manager.get_theme()["colors"]["background"])
        bottom_frame.pack(fill=tk.BOTH, expand=True)
        
        # Section activité
        activity_frame = ttk.LabelFrame(
            bottom_frame,
            text="📈 Activité Récente",
            style="Modern.TLabelframe",
            padding="15"
        )
        activity_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Section indicateurs
        indicators_frame = ttk.LabelFrame(
            bottom_frame,
            text="🎯 Indicateurs Clés",
            style="Modern.TLabelframe",
            padding="15"
        )
        indicators_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Contenu de test
        self.create_test_content(activity_frame, indicators_frame)
    
    def create_test_content(self, activity_frame, indicators_frame):
        """Créer du contenu de test"""
        # Table d'activité
        columns = ('Type', 'Date', 'Description', 'Valeur')
        tree = ttk.Treeview(activity_frame, columns=columns, show='headings', height=8, style="Modern.Treeview")
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)
        
        # Données de test
        test_data = [
            ('📦 Réception', '2025-01-28', 'Voyage 12345', '1,250 T'),
            ('🏭 Production', '2025-01-28', 'Cycle matinal', '850 T'),
            ('💰 Vente', '2025-01-27', 'Client ABC', '500 T'),
            ('⚠️ Arrêt', '2025-01-27', 'Maintenance', '2h'),
        ]
        
        for i, data in enumerate(test_data):
            tree.insert('', tk.END, values=data)
        
        tree.pack(fill=tk.BOTH, expand=True)
        
        # Indicateurs de test
        theme = self.theme_manager.get_theme()
        indicators = [
            ("Qualité P2O5", "29.2%", "🎯"),
            ("Disponibilité", "87.5%", "⚡"),
            ("Rendement", "78.3%", "📈"),
            ("Système", "✅ OK", "💾")
        ]
        
        for label, value, icon in indicators:
            indicator_frame = tk.Frame(indicators_frame, bg=theme["colors"]["background"])
            indicator_frame.pack(fill=tk.X, pady=5)
            
            icon_label = tk.Label(
                indicator_frame,
                text=icon,
                bg=theme["colors"]["background"],
                font=("Segoe UI", 16)
            )
            icon_label.pack(side=tk.LEFT, padx=(0, 10))
            
            text_frame = tk.Frame(indicator_frame, bg=theme["colors"]["background"])
            text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
            
            label_widget = tk.Label(
                text_frame,
                text=label,
                bg=theme["colors"]["background"],
                fg=theme["colors"]["text_secondary"],
                font=theme["fonts"]["small"],
                anchor="w"
            )
            label_widget.pack(anchor="w")
            
            value_widget = tk.Label(
                text_frame,
                text=value,
                bg=theme["colors"]["background"],
                fg=theme["colors"]["text_primary"],
                font=theme["fonts"]["body"],
                anchor="w"
            )
            value_widget.pack(anchor="w")
    
    def create_status_bar(self):
        """Créer la barre de statut"""
        from themes import StatusBar
        self.status_bar = StatusBar(self.root, self.theme_manager)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar.set_status("Interface de test chargée", "success")
    
    def run(self):
        """Lancer l'application"""
        self.root.mainloop()

def main():
    """Fonction principale"""
    app = TestDesignApp()
    app.run()

if __name__ == "__main__":
    main()
