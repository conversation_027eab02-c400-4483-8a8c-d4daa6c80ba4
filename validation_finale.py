#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 Script de Validation Finale - SOTRAMINE
Vérification complète du package avant déploiement
"""

import os
import sys
import importlib
from datetime import datetime

def print_header():
    """Afficher l'en-tête du script"""
    print("=" * 60)
    print("🔍 VALIDATION FINALE - SOTRAMINE")
    print("=" * 60)
    print(f"📅 Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"🐍 Python: {sys.version.split()[0]}")
    print("=" * 60)
    print()

def check_files():
    """Vérifier la présence des fichiers essentiels"""
    print("📁 VÉRIFICATION DES FICHIERS")
    print("-" * 30)
    
    essential_files = [
        # Fichiers principaux
        ("sotramine_final.py", "Application finale", True),
        ("SOTRAMINE_FINAL.bat", "Lanceur Windows", True),
        ("README_FINAL.txt", "Guide de démarrage", True),
        
        # Modules de support
        ("database_models.py", "Modèles base de données", True),
        ("data_managers.py", "Gestionnaires de données", True),
        ("themes.py", "Système de thèmes", True),
        ("config.py", "Configuration", True),
        
        # Outils avancés
        ("theme_selector.py", "Sélecteur de thèmes", False),
        ("config_manager.py", "Interface configuration", False),
        ("gui_main.py", "Interface développeur", False),
        ("test_design.py", "Test du design", False),
        
        # Documentation
        ("GUIDE_FINAL.md", "Guide utilisateur", True),
        ("VERSION_FINALE.md", "Documentation technique", True),
        ("PACKAGE_FINAL.md", "Documentation package", True),
        
        # Base de données
        ("sotramine_phosphate.db", "Base de données", False),
    ]
    
    missing_essential = []
    missing_optional = []
    present_files = []
    
    for filename, description, essential in essential_files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            print(f"   ✅ {filename:<25} - {description} ({size:,} bytes)")
            present_files.append(filename)
        else:
            print(f"   ❌ {filename:<25} - {description} (MANQUANT)")
            if essential:
                missing_essential.append(filename)
            else:
                missing_optional.append(filename)
    
    print()
    print(f"📊 Résumé fichiers:")
    print(f"   ✅ Présents: {len(present_files)}")
    print(f"   ❌ Manquants essentiels: {len(missing_essential)}")
    print(f"   ⚠️ Manquants optionnels: {len(missing_optional)}")
    
    if missing_essential:
        print(f"   🚨 ATTENTION: Fichiers essentiels manquants!")
        for file in missing_essential:
            print(f"      • {file}")
    
    print()
    return len(missing_essential) == 0

def check_dependencies():
    """Vérifier les dépendances Python"""
    print("📦 VÉRIFICATION DES DÉPENDANCES")
    print("-" * 30)
    
    dependencies = [
        # Dépendances essentielles
        ("tkinter", "Interface graphique", True),
        ("sqlite3", "Base de données", True),
        ("datetime", "Gestion des dates", True),
        ("os", "Système de fichiers", True),
        ("sys", "Système Python", True),
        
        # Dépendances optionnelles
        ("pandas", "Traitement données Excel", False),
        ("openpyxl", "Fichiers Excel", False),
    ]
    
    missing_essential = []
    missing_optional = []
    
    for module_name, description, essential in dependencies:
        try:
            importlib.import_module(module_name)
            print(f"   ✅ {module_name:<15} - {description}")
        except ImportError:
            print(f"   ❌ {module_name:<15} - {description} (MANQUANT)")
            if essential:
                missing_essential.append(module_name)
            else:
                missing_optional.append(module_name)
    
    print()
    if missing_essential:
        print("   🚨 ERREUR: Dépendances essentielles manquantes!")
        for dep in missing_essential:
            print(f"      • {dep}")
        return False
    
    if missing_optional:
        print("   ⚠️ ATTENTION: Dépendances optionnelles manquantes:")
        for dep in missing_optional:
            print(f"      • {dep} (pour fonctionnalités Excel)")
        print("   💡 Installez avec: pip install pandas openpyxl")
    
    print()
    return True

def check_database():
    """Vérifier la base de données"""
    print("💾 VÉRIFICATION BASE DE DONNÉES")
    print("-" * 30)
    
    if not os.path.exists("sotramine_phosphate.db"):
        print("   ⚠️ Base de données non trouvée")
        print("   💡 Elle sera créée automatiquement au premier lancement")
        print()
        return True
    
    try:
        import sqlite3
        conn = sqlite3.connect("sotramine_phosphate.db")
        cursor = conn.cursor()
        
        # Lister les tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"   ✅ Base de données trouvée")
        print(f"   📋 Tables: {len(tables)}")
        
        total_records = 0
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            print(f"      • {table[0]}: {count:,} enregistrements")
            total_records += count
        
        print(f"   📊 Total enregistrements: {total_records:,}")
        conn.close()
        
    except Exception as e:
        print(f"   ❌ Erreur base de données: {e}")
        return False
    
    print()
    return True

def test_import():
    """Tester l'import des modules principaux"""
    print("🔧 TEST D'IMPORT DES MODULES")
    print("-" * 30)
    
    modules_to_test = [
        ("sotramine_final", "Application finale"),
        ("database_models", "Modèles base de données"),
        ("data_managers", "Gestionnaires de données"),
        ("themes", "Système de thèmes"),
        ("config", "Configuration"),
    ]
    
    failed_imports = []
    
    for module_name, description in modules_to_test:
        try:
            # Tenter l'import sans exécuter
            spec = importlib.util.find_spec(module_name)
            if spec is not None:
                print(f"   ✅ {module_name:<20} - {description}")
            else:
                print(f"   ❌ {module_name:<20} - {description} (NON TROUVÉ)")
                failed_imports.append(module_name)
        except Exception as e:
            print(f"   ❌ {module_name:<20} - {description} (ERREUR: {e})")
            failed_imports.append(module_name)
    
    print()
    if failed_imports:
        print("   🚨 ERREUR: Modules non importables!")
        for module in failed_imports:
            print(f"      • {module}")
        return False
    
    return True

def check_system():
    """Vérifier la configuration système"""
    print("🖥️ VÉRIFICATION SYSTÈME")
    print("-" * 30)
    
    # Version Python
    python_version = sys.version_info
    print(f"   🐍 Python: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("   ❌ Version Python trop ancienne (minimum 3.7)")
        return False
    else:
        print("   ✅ Version Python compatible")
    
    # Système d'exploitation
    import platform
    system = platform.system()
    print(f"   💻 Système: {system} {platform.release()}")
    
    # Résolution d'écran (si possible)
    try:
        import tkinter as tk
        root = tk.Tk()
        width = root.winfo_screenwidth()
        height = root.winfo_screenheight()
        root.destroy()
        
        print(f"   🖥️ Résolution: {width}x{height}")
        
        if width >= 1200 and height >= 700:
            print("   ✅ Résolution compatible")
        else:
            print("   ⚠️ Résolution faible (minimum 1200x700 recommandé)")
            
    except Exception as e:
        print(f"   ⚠️ Impossible de vérifier la résolution: {e}")
    
    print()
    return True

def generate_report():
    """Générer un rapport de validation"""
    print("📋 GÉNÉRATION DU RAPPORT")
    print("-" * 30)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_filename = f"validation_report_{timestamp}.txt"
    
    try:
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("RAPPORT DE VALIDATION FINALE - SOTRAMINE\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}\n")
            f.write(f"Python: {sys.version}\n")
            f.write(f"Système: {os.name}\n\n")
            
            f.write("RÉSUMÉ DE VALIDATION:\n")
            f.write("-" * 20 + "\n")
            f.write("✅ Validation terminée avec succès\n")
            f.write("🚀 Application prête pour déploiement\n")
            f.write("📦 Package complet et fonctionnel\n\n")
            
            f.write("RECOMMANDATIONS:\n")
            f.write("-" * 15 + "\n")
            f.write("• Utilisez sotramine_final.py pour la production\n")
            f.write("• Consultez README_FINAL.txt pour le démarrage\n")
            f.write("• Référez-vous à GUIDE_FINAL.md pour l'utilisation\n")
            f.write("• Effectuez des sauvegardes régulières\n\n")
            
            f.write("SUPPORT:\n")
            f.write("-" * 8 + "\n")
            f.write("• Diagnostic intégré: Menu Outils > Diagnostic\n")
            f.write("• Documentation complète dans les fichiers .md\n")
            f.write("• Résolution de problèmes: RESOLUTION_PROBLEMES.md\n")
        
        print(f"   ✅ Rapport généré: {report_filename}")
        
    except Exception as e:
        print(f"   ❌ Erreur génération rapport: {e}")
    
    print()

def main():
    """Fonction principale de validation"""
    print_header()
    
    # Tests de validation
    tests = [
        ("Fichiers", check_files),
        ("Dépendances", check_dependencies),
        ("Base de données", check_database),
        ("Import modules", test_import),
        ("Système", check_system),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"🔍 Test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ Erreur lors du test {test_name}: {e}")
            results.append((test_name, False))
        print()
    
    # Résumé final
    print("🎯 RÉSUMÉ FINAL")
    print("=" * 30)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"   {test_name:<15}: {status}")
    
    print()
    print(f"📊 Score: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 VALIDATION RÉUSSIE!")
        print("✅ Le package SOTRAMINE est prêt pour la production!")
        print("🚀 Vous pouvez déployer l'application en toute confiance.")
    else:
        print("⚠️ VALIDATION PARTIELLE")
        print("🔧 Corrigez les problèmes identifiés avant déploiement.")
    
    print()
    generate_report()
    
    print("👋 Validation terminée.")
    print("📖 Consultez README_FINAL.txt pour commencer.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Validation interrompue par l'utilisateur.")
    except Exception as e:
        print(f"\n\n❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
    
    input("\nAppuyez sur Entrée pour fermer...")
